# C/C++ build system timings
generate_cxx_metadata
  [gap of 61ms]
  create-invalidation-state 177ms
  [gap of 44ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 307ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 63ms
  [gap of 30ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 118ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 50ms
  [gap of 31ms]
generate_cxx_metadata completed in 96ms

