package com.airdoc.mpd.detection

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.print.PrintAttributes
import android.print.PrintManager
import android.util.SparseArray
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.source.ConcatenatingMediaSource
import androidx.media3.exoplayer.source.MediaSource
import com.airdoc.component.common.base.BaseCommonActivity
import com.airdoc.component.common.glide.ImageLoader
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.WebViewManager
import com.airdoc.mpd.R
import com.airdoc.mpd.detection.hrv.HrvWebView
import com.airdoc.mpd.device.DeviceManager
import com.airdoc.mpd.device.enumeration.StartupMode
import com.airdoc.mpd.gaze.GazeConstants
import com.airdoc.mpd.gaze.track.GazeTrackService
import com.airdoc.mpd.gaze.track.GazeWebSocketService
import com.airdoc.mpd.media.PlayManager
import com.airdoc.mpd.media.bean.RawMedia
import com.airdoc.mpd.ppg.bean.PPGDataPoint
import com.airdoc.mpd.ppg.vm.PpgViewModel
import com.airdoc.mpd.user.UserManager
import com.airdoc.mpd.user.bean.DetectionProject
import com.airdoc.mpd.user.bean.User
import com.airdoc.mpd.user.enumeration.Gender
import com.airdoc.mpd.user.vm.UserViewModel
import com.airdoc.mpd.utils.NetworkUtils
import com.google.gson.Gson
import com.lepu.blepro.event.EventMsgConst
import com.lepu.blepro.event.InterfaceEvent
import com.lepu.blepro.ext.BleServiceHelper
import com.lepu.blepro.ext.pc60fw.RtParam
import com.lepu.blepro.ext.pc60fw.RtWave
import com.lepu.blepro.objs.Bluetooth
import com.lepu.blepro.objs.BluetoothController
import com.lepu.blepro.observer.BIOL
import com.lepu.blepro.observer.BleChangeObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.MalformedURLException
import java.net.URL

/**
 * FileName: DetectionActivity1
 * Author by lilin,Date on 2025/5/27 19:25
 * PS: Not easy to write code, please indicate.
 */
class DetectionActivity1 : BaseCommonActivity(), HrvWebView.HrvActionListener, BleChangeObserver {

    companion object{
        private val TAG = DetectionActivity1::class.java.simpleName
        private const val INPUT_PARAM_ACCESS_TOKEN = "accessToken"
        private const val INPUT_PARAM_TOKEN_TYPE = "tokenType"

        const val PARAM_IS_GO_HOME = "IS_GO_HOME"

        fun createIntent(context: Context, accessToken:String, tokenType:String): Intent {
            val intent = Intent(context, DetectionActivity1::class.java)
            intent.putExtra(INPUT_PARAM_ACCESS_TOKEN,accessToken)
            intent.putExtra(INPUT_PARAM_TOKEN_TYPE,tokenType)
            return intent
        }
    }

    private val clDetectionRoot by id<ConstraintLayout>(R.id.cl_detection_root)
    private val ivLogo by id<ImageView>(R.id.iv_logo)
    private val ivUserAvatar by id<ImageView>(R.id.iv_user_avatar)
    private val tvUserName by id<TextView>(R.id.tv_user_name)
    private val tvUserGender by id<TextView>(R.id.tv_user_gender)
    private val tvUserPhone by id<TextView>(R.id.tv_user_phone)
    private val tvDetectionCode by id<TextView>(R.id.tv_detection_code)
    private val tvCancelDetection by id<TextView>(R.id.tv_cancel_detection)
    private val clContent by id<ConstraintLayout>(R.id.cl_content)
    private val wbHrv by id<HrvWebView>(R.id.wb_hrv)
    private val llLoading by id<LinearLayout>(R.id.ll_loading)

    private val userVM by viewModels<UserViewModel>()
    private val ppgVM by viewModels<PpgViewModel>()
    private var projects = mutableListOf<DetectionProject>()
    private var mAccessToken = ""
    private var mTokenType = ""

    // 眼动追踪状态管理
    private var isGazeTrackingActive = false

    // PPG相关变量
    private var ppgWebSocketService: GazeWebSocketService? = null
    private val ppgDataPointList = mutableListOf<PPGDataPoint>()
    private val gson = Gson()
    private var lastDataReceivedTime = 0L
    private var isDataCollectionStarted = false
    private var isComplete = false
    private var isTerminate = false

    // PPG数据超时处理
    private val ppgTimeoutHandler = Handler(Looper.getMainLooper())
    private var ppgTimeoutRunnable: Runnable? = null
    private val PPG_DATA_TIMEOUT = 30000L // 30秒超时

    // 支持的PPG设备模型
    private val supportedModels = intArrayOf(Bluetooth.MODEL_PC60FW)


    private val handler = object :  LifecycleHandler(Looper.getMainLooper(),this) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }

    private fun parseMessage(msg: Message) {
        when(msg.what) {
            GazeConstants.MSG_GAZE_TRACKING_STATE -> {
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "收到眼动追踪状态更新: $state")

                lifecycleScope.launch {
                    if (state) {
                        // 眼动追踪启动成功
                        isGazeTrackingActive = true
                        wbHrv.evaluateJavascript("javascript:onGazeTrackingStarted(true)", null)
                        Logger.i(TAG, msg = "✓ 眼动追踪启动成功确认，已通知Web页面")
                    } else {
                        // 眼动追踪停止
                        isGazeTrackingActive = false
                        wbHrv.evaluateJavascript("javascript:onGazeTrackingStopped()", null)
                        Logger.i(TAG, msg = "✓ 眼动追踪停止确认，已通知Web页面")
                    }
                }
            }
            else -> {
                Logger.d(TAG, msg = "收到未处理的消息: ${msg.what}")
            }
        }
    }


    var mServiceManager : Messenger? = null

    private var mClientMessage: Messenger = Messenger(handler)


    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "DetectionActivity1 onServiceConnected")
            if (service != null){
                mServiceManager = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceManager?.send(message)

                // 服务连接成功后立即发送开启相机消息
                Logger.d(TAG, msg = "📷 服务连接成功，发送开启相机消息")
                val cameraMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_ON_CAMERA
                    replyTo = mClientMessage
                }
                mServiceManager?.send(cameraMessage)
                Logger.d(TAG, msg = "✓ 已发送开启相机消息")
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "DetectionActivity1 onServiceDisconnected")
            mServiceManager = null
        }
    }


    //检测Web页启动器
    private var detectionWebLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK){
            val data = result.data
            val isGoHome = data?.getBooleanExtra(PARAM_IS_GO_HOME,false)?:false
            if (isGoHome){
                finish()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WebViewManager.hookWebView()
        setContentView(R.layout.activity_detection1)

        // 启动眼动追踪前台服务（但不在服务中自动启动相机和追踪）
        startForegroundService(Intent(this, GazeTrackService::class.java))

        this.bindService(Intent(this, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)

        // 设置返回键处理
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPress()
            }
        })

        initParam()
        initView()
        initObserver()
        initData()
        initPpgService()
        startPpgWebSocketServer()
    }







    private fun initParam() {
        mAccessToken = intent.getStringExtra(INPUT_PARAM_ACCESS_TOKEN) ?: ""
        mTokenType = intent.getStringExtra(INPUT_PARAM_TOKEN_TYPE) ?: ""
    }

    private fun initView() {
        initListener()

        val logo = DeviceManager.getDeviceInfo()?.logo
        if (!logo.isNullOrEmpty()){
            ImageLoader.loadImageWithPlaceholder(this,logo,0,R.drawable.ic_main_logo,ivLogo)
        }else{
            ivLogo.setImageResource(R.drawable.ic_main_logo)
        }

        // 添加原有的android接口
        wbHrv.addJavascriptInterface(wbHrv.HrvAction(),"android")

        wbHrv.setHrvActionListener(this)

        wbHrv.setBackgroundColor(Color.TRANSPARENT)

        clearWebViewCache()

    }

    private fun initListener() {
        tvCancelDetection.setOnSingleClickListener {
            finish()
        }
    }

    /**
     * 处理返回键事件，确保在退出前关闭眼动追踪
     */
    private fun handleBackPress() {
        Logger.d(TAG, msg = "🔙 DetectionActivity1 - 收到返回键事件")
        Logger.d(TAG, msg = "  当前眼动追踪状态: $isGazeTrackingActive")

        // 如果眼动追踪正在运行，先停止眼动追踪
        if (isGazeTrackingActive) {
            Logger.d(TAG, msg = "  眼动追踪正在运行，先停止眼动追踪再退出")
            lifecycleScope.launch {
                try {
                    // 调用停止眼动追踪方法
                    onStopGazeTracking()
                    Logger.d(TAG, msg = "  ✓ 眼动追踪已停止，延迟后退出Activity")

                    // 解绑服务连接
                    try {
                        Logger.d(TAG, msg = "  解绑GazeTrackService服务连接")
                        unbindService(serviceConnection)
                        mServiceManager = null
                        Logger.d(TAG, msg = "  ✓ GazeTrackService服务连接已解绑")
                    } catch (e: Exception) {
                        Logger.e(TAG, msg = "  解绑GazeTrackService服务连接异常: ${e.message}")
                    }

                    // 停止GazeTrackService前台服务以释放资源
                    try {
                        Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
                        stopService(Intent(this@DetectionActivity1, GazeTrackService::class.java))
                        Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
                    } catch (e: Exception) {
                        Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
                    }

                    // 使用finish()来正常退出
                    finish()
                    Logger.d(TAG, msg = "  ✓ Activity已退出")
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "  停止眼动追踪异常: ${e.message}")
                    // 即使停止失败也要退出Activity
                    finish()
                }
            }
        } else {
            Logger.d(TAG, msg = "  眼动追踪未运行，直接退出Activity")

            // 解绑服务连接
            try {
                Logger.d(TAG, msg = "  解绑GazeTrackService服务连接")
                unbindService(serviceConnection)
                mServiceManager = null
                Logger.d(TAG, msg = "  ✓ GazeTrackService服务连接已解绑")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  解绑GazeTrackService服务连接异常: ${e.message}")
            }

            // 停止GazeTrackService前台服务以释放资源
            try {
                Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
                stopService(Intent(this, GazeTrackService::class.java))
                Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
            }

            // 眼动追踪未运行，直接退出
            finish()
        }
    }

    private fun initObserver() {
        userVM.userLiveData.observe(this){
            updateUserInfo(it)
        }
        userVM.detectionProjectsLiveData.observe(this){
            projects.addAll(it?.projects?: emptyList())
            if (projects.isNotEmpty()){
                val detectionProject = projects[0]
                Logger.d(TAG, msg = "detectionProjectsLiveData detectionProject = $detectionProject")
//                val url = detectionProject.url?:""
                val url = "http://10.100.4.111:5173/hrvtest"
                val code = detectionProject.code?:""
                if (code == "HRV"){
                    loadUrl(url)
                }
            }
        }
    }

    private fun initData() {
        userVM.getUserInfo("$mTokenType $mAccessToken")
        userVM.getDetectionProjects("$mTokenType $mAccessToken")
    }

    /**
     * 初始化PPG服务
     */
    private fun initPpgService() {
        Logger.d(TAG, msg = "初始化PPG服务")

        // 检查BLE服务是否已初始化
        val checkService = BleServiceHelper.BleServiceHelper.checkService()
        if (!checkService) {
            Logger.d(TAG, msg = "初始化BLE服务")
            // 配置原始数据保存路径
            val rawFolders = SparseArray<String>()
            rawFolders.set(Bluetooth.MODEL_PC60FW, "${getExternalFilesDir(null)?.absolutePath}/pc60fw")

            // 初始化服务
            BleServiceHelper.BleServiceHelper
                .initRawFolder(rawFolders)
                .initService(application)
                .initLog(false)

            Logger.d(TAG, msg = "BLE服务初始化完成")
        }

        // 添加生命周期观察者
        lifecycle.addObserver(BIOL(this, supportedModels))
        Logger.d(TAG, msg = "PPG服务初始化完成")
    }

    /**
     * 启动PPG WebSocket服务器
     */
    private fun startPpgWebSocketServer() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // 如果WebSocket服务已经启动，先停止
                ppgWebSocketService?.let {
                    Logger.d(TAG, msg = "PPG WebSocket服务已存在，先停止旧服务")
                    it.stop()
                    ppgWebSocketService = null
                }

                // 动态获取设备IP地址
                val deviceIP = NetworkUtils.getDeviceIPAddress()
                val port = 9201 // 使用不同的端口避免与眼动数据冲突

                Logger.d(TAG, msg = "准备启动PPG WebSocket服务器")
                Logger.d(TAG, msg = "  设备IP: $deviceIP")
                Logger.d(TAG, msg = "  端口: $port")

                val inetSocketAddress = if (deviceIP.isNotEmpty()) {
                    InetSocketAddress(deviceIP as String, port)
                } else {
                    // 如果无法获取IP，绑定到所有接口
                    Logger.w(TAG, msg = "无法获取设备IP，绑定到所有接口")
                    InetSocketAddress(port)
                }

                ppgWebSocketService = GazeWebSocketService(inetSocketAddress).apply {
                    isReuseAddr = true
                }

                ppgWebSocketService?.start()
                Logger.i(TAG, msg = "✓ PPG WebSocket服务器启动成功")
                Logger.i(TAG, msg = "  监听地址: ${inetSocketAddress.address?.hostAddress}:${inetSocketAddress.port}")

            } catch (e: Exception) {
                Logger.e(TAG, msg = "启动PPG WebSocket服务器失败: ${e.message}")
            }
        }
    }

    /**
     * 开始扫描PPG设备
     */
    private fun startPpgScan() {
        Logger.d(TAG, msg = "开始扫描PPG设备")
        BleServiceHelper.BleServiceHelper.startScan(supportedModels)
    }

    /**
     * 停止扫描PPG设备
     */
    private fun stopPpgScan() {
        Logger.d(TAG, msg = "停止扫描PPG设备")
        BleServiceHelper.BleServiceHelper.stopScan()
    }

    /**
     * 连接PPG设备
     */
    private fun connectPpgDevice(bluetooth: Bluetooth) {
        Logger.d(TAG, msg = "连接PPG设备: ${bluetooth.name}")

        // 设置接口
        BleServiceHelper.BleServiceHelper.setInterfaces(bluetooth.model)

        // 停止扫描
        stopPpgScan()

        // 连接设备
        BleServiceHelper.BleServiceHelper.connect(this, bluetooth.model, bluetooth.device)
        BluetoothController.clear()

        // 连接设备后开始超时监控
        Logger.d(TAG, msg = "PPG设备连接中，开始超时监控")
        resetPpgTimeout()
    }

    /**
     * 处理PPG数据接收
     */
    private fun handlePpgDataReceived(data: RtWave) {
        // 更新最后接收数据的时间
        lastDataReceivedTime = System.currentTimeMillis()

        // 重置超时计时器
        resetPpgTimeout()

        val ints = data.waveIntData.toList()
        ints.forEachIndexed { index, value ->
            if (ppgDataPointList.isEmpty()) {
                ppgDataPointList.add(PPGDataPoint(value.toDouble(), System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L))
            } else {
                // 间隔20毫秒，转成纳秒
                ppgDataPointList.add(PPGDataPoint(value.toDouble(), ppgDataPointList[ppgDataPointList.size - 1].timestamp + 20 * 1_000_000L))
            }
        }

        // 广播PPG数据到WebSocket
        broadcastPpgDataToWebSocket(ints)
    }

    /**
     * 广播PPG数据到WebSocket
     */
    private fun broadcastPpgDataToWebSocket(ppgValues: List<Int>) {
        try {
            ppgWebSocketService?.let { service ->
                // 构建PPG数据JSON
                val ppgData = mapOf(
                    "type" to "ppg_data",
                    "timestamp" to System.currentTimeMillis(),
                    "values" to ppgValues,
                    "sampleRate" to 50 // PC60FW设备采样率为50Hz
                )

                val jsonData = gson.toJson(ppgData)

                // 广播数据
                service.broadcast(jsonData)

                Logger.d(TAG, msg = "PPG数据已广播到WebSocket: ${ppgValues.size}个数据点")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "广播PPG数据到WebSocket失败: ${e.message}")
        }
    }

    /**
     * 重置PPG数据超时计时器
     */
    private fun resetPpgTimeout() {
        // 取消之前的超时任务
        cancelPpgTimeout()

        // 创建新的超时任务
        ppgTimeoutRunnable = Runnable {
            Logger.w(TAG, msg = "PPG数据接收超时，自动停止数据收集")
            if (!isComplete && isDataCollectionStarted) {
                completePpgEvaluation()
            }
        }

        // 设置超时延迟
        ppgTimeoutHandler.postDelayed(ppgTimeoutRunnable!!, PPG_DATA_TIMEOUT)
        Logger.d(TAG, msg = "PPG数据超时计时器已重置，超时时间: ${PPG_DATA_TIMEOUT}ms")
    }

    /**
     * 取消PPG数据超时计时器
     */
    private fun cancelPpgTimeout() {
        ppgTimeoutRunnable?.let {
            ppgTimeoutHandler.removeCallbacks(it)
            ppgTimeoutRunnable = null
            Logger.d(TAG, msg = "PPG数据超时计时器已取消")
        }
    }

    /**
     * 完成PPG数据评估
     */
    private fun completePpgEvaluation() {
        Logger.d(TAG, msg = "开始完成PPG数据评估，数据点数量: ${ppgDataPointList.size}")
        isComplete = true
        isTerminate = false

        // 取消超时计时器
        cancelPpgTimeout()

        // 通知WebView PPG数据收集完成
        lifecycleScope.launch {
            try {
                val completionData = mapOf(
                    "type" to "ppg_collection_complete",
                    "timestamp" to System.currentTimeMillis(),
                    "totalDataPoints" to ppgDataPointList.size
                )

                val jsonData = gson.toJson(completionData)
                ppgWebSocketService?.broadcast(jsonData)

                Logger.d(TAG, msg = "PPG数据收集完成通知已发送到WebSocket")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "发送PPG完成通知失败: ${e.message}")
            }
        }

        Logger.d(TAG, msg = "PPG数据评估完成")
    }

    private fun updateUserInfo(user: User?){
        val userGender = UserManager.getUserGender(user)
        when(userGender){
            Gender.FEMALE ->{
                ImageLoader.loadImageWithPlaceholder(this,user?.avatar?:"",R.drawable.ic_female_avatar_round,R.drawable.ic_female_avatar_round,ivUserAvatar)
            }
            else ->{
                ImageLoader.loadImageWithPlaceholder(this,user?.avatar?:"",R.drawable.ic_male_avatar_round,R.drawable.ic_male_avatar_round,ivUserAvatar)
            }
        }
        tvUserName.isVisible = user?.username.orEmpty().isNotBlank()
        tvUserName.text = user?.username

        tvUserGender.isVisible = userGender != null
        tvUserGender.text = getString(R.string.str_gender_, when(userGender){
            Gender.MALE -> getString(R.string.str_gender_male)
            Gender.FEMALE -> getString(R.string.str_gender_female)
            else -> getString(R.string.str_confidential)
        })

        tvUserPhone.isVisible = user?.phone.orEmpty().isNotBlank()
        tvUserPhone.text = getString(R.string.str_phone_last_number_, user?.phone?:"")

        tvDetectionCode.isVisible = user?.id != null
        tvDetectionCode.text = getString(R.string.str_detection_code_s, user?.id.toString())

        user?.let {
            playPromptVoice(it)
        }
    }

    private fun loadUrl(url:String){
        if (url.isNotBlank()){
            // 在加载URL前清除WebView缓存
            wbHrv.loadUrl(url)
        }
    }

    /**
     * 清除WebView缓存
     */
    private fun clearWebViewCache() {
        wbHrv.clearWebViewCache()
    }

    /**
     * 播放提示语言
     */
    @OptIn(UnstableApi::class)
    private fun playPromptVoice(user: User){
        Logger.d(TAG, msg = "playPromptVoice user = $user")
        val mediaSources = mutableListOf<MediaSource>()
        when(DeviceManager.getStartupMode()){
            StartupMode.QRCODE_WECHAT,StartupMode.QRCODE_H5,StartupMode.QRCODE_WHATSAPP ->{
                val phoneNumList = user.phone.orEmpty().map { it - '0' }
                if (phoneNumList.size != 4) return
                val phoneLastNumSpeech = RawMedia(R.raw.phone_last_number).createMediaSource(this)
                if (phoneLastNumSpeech != null) mediaSources.add(phoneLastNumSpeech)
                phoneNumList.forEach {
                    val phoneNumSpeech = getPhoneNumSpeech(it)
                    if (phoneNumSpeech != null) mediaSources.add(phoneNumSpeech)
                }
            }
            StartupMode.ACCESS_CODE ->{
                val idLastFour = user.id.toString().replace("-", "")
                if (idLastFour.length < 4) return
                val codeLastNumSpeech = RawMedia(R.raw.detection_code_last_number).createMediaSource(this)
                if (codeLastNumSpeech != null) mediaSources.add(codeLastNumSpeech)
                idLastFour.padStart(4, '0')// 补零到4位
                    .takeLast(4)
                    .map { it - '0' }
                    .forEach {
                        val phoneNumSpeech = getPhoneNumSpeech(it)
                        if (phoneNumSpeech != null) mediaSources.add(phoneNumSpeech)
                    }
            }
            else ->{
            }
        }
        val proceedDetection = RawMedia(R.raw.proceed_detection).createMediaSource(this)
        if (proceedDetection != null) mediaSources.add(proceedDetection)
        if (mediaSources.isEmpty()) return
        val concatenatingMediaSource = ConcatenatingMediaSource(*mediaSources.toTypedArray())
        PlayManager.playMediaSource(concatenatingMediaSource)
    }

    private fun getPhoneNumSpeech(phoneNum:Int): MediaSource?{
        return when(phoneNum){
            0 -> return RawMedia(R.raw.speech_0).createMediaSource(this)
            1 -> return RawMedia(R.raw.speech_1).createMediaSource(this)
            2 -> return RawMedia(R.raw.speech_2).createMediaSource(this)
            3 -> return RawMedia(R.raw.speech_3).createMediaSource(this)
            4 -> return RawMedia(R.raw.speech_4).createMediaSource(this)
            5 -> return RawMedia(R.raw.speech_5).createMediaSource(this)
            6 -> return RawMedia(R.raw.speech_6).createMediaSource(this)
            7 -> return RawMedia(R.raw.speech_7).createMediaSource(this)
            8 -> return RawMedia(R.raw.speech_8).createMediaSource(this)
            9 -> return RawMedia(R.raw.speech_9).createMediaSource(this)
            else -> null
        }
    }

    override fun onPageFinished() {
        lifecycleScope.launch {
            llLoading.isVisible = false
        }
    }

    override fun onFinish() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun goHome() {
        lifecycleScope.launch {
            finish()
        }
    }

    override fun onPrintPage() {
        lifecycleScope.launch {
            createWebPrintJob(wbHrv)
        }
    }

    override fun onReady() {
        lifecycleScope.launch {
            toggleFullScreen(true)
        }
    }

    override fun onStartGazeTracking() {
        Logger.d(TAG, msg = "🚀 DetectionActivity1 - 收到启动眼动追踪请求")
        Logger.d(TAG, msg = "  当前状态: isGazeTrackingActive = $isGazeTrackingActive")

        if (isGazeTrackingActive) {
            Logger.w(TAG, msg = "眼动追踪已经在运行中，忽略重复启动请求")
            wbHrv.evaluateJavascript("javascript:onGazeTrackingStarted(true)", null)
            return
        }

        lifecycleScope.launch {
            try {
                Logger.d(TAG, msg = "通过消息通信启动相机和眼动追踪...")

                // 检查服务连接状态
                if (mServiceManager == null) {
                    Logger.e(TAG, msg = "服务未连接，无法启动眼动追踪")
                    wbHrv.evaluateJavascript("javascript:onGazeTrackingStarted(false)", null)
                    return@launch
                }

                // 1. 先发送开启相机消息
                val cameraMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_ON_CAMERA
                    replyTo = mClientMessage
                }

                mServiceManager?.send(cameraMessage)
                Logger.d(TAG, msg = "已发送开启相机消息")

                val trackMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_START_TRACK
                    replyTo = mClientMessage
                }

                mServiceManager?.send(trackMessage)

                Logger.d(TAG, msg = "已发送开始追踪消息")

                // 暂时设置为成功状态，实际状态会通过服务回调更新
                isGazeTrackingActive = true
                wbHrv.evaluateJavascript("javascript:onGazeTrackingStarted(true)", null)
                Logger.i(TAG, msg = "✓ 眼动追踪启动消息已发送，已通知Web页面")

            } catch (e: Exception) {
                Logger.e(TAG, msg = "启动眼动追踪异常: ${e.message}")
                wbHrv.evaluateJavascript("javascript:onGazeTrackingStarted(false)", null)
            }
        }
    }

    override fun onStopGazeTracking() {
        Logger.d(TAG, msg = "🛑 DetectionActivity1 - 收到停止眼动追踪请求")
        Logger.d(TAG, msg = "  当前状态: isGazeTrackingActive = $isGazeTrackingActive")

        if (!isGazeTrackingActive) {
            Logger.w(TAG, msg = "眼动追踪未在运行，忽略停止请求")
            wbHrv.evaluateJavascript("javascript:onGazeTrackingStopped()", null)
            return
        }

        lifecycleScope.launch {
            try {
                Logger.d(TAG, msg = "通过消息通信停止眼动追踪和相机...")

                // 检查服务连接状态
                if (mServiceManager == null) {
                    Logger.e(TAG, msg = "服务未连接，无法停止眼动追踪")
                    return@launch
                }

                // 1. 先发送停止追踪消息
                val stopTrackMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_STOP_TRACK
                    replyTo = mClientMessage
                }
                mServiceManager?.send(stopTrackMessage)
                Logger.d(TAG, msg = "已发送停止追踪消息")


                val stopCameraMessage = Message.obtain().apply {
                    what = GazeConstants.MSG_TURN_OFF_CAMERA
                    replyTo = mClientMessage
                }
                mServiceManager?.send(stopCameraMessage)
                Logger.d(TAG, msg = "已发送关闭相机消息")

                // 更新状态
                isGazeTrackingActive = false
                Logger.i(TAG, msg = "✓ 眼动追踪停止消息已发送，状态已更新")

                // 通知Web页面停止成功
                wbHrv.evaluateJavascript("javascript:onGazeTrackingStopped()", null)
                Logger.d(TAG, msg = "已通知Web页面眼动追踪停止")

            } catch (e: Exception) {
                Logger.e(TAG, msg = "停止眼动追踪异常: ${e.message}")
            }
        }
    }

    override fun onGazeTrackingStatus(isEnabled: Boolean) {
        Logger.d(TAG, msg = "⚙️ DetectionActivity1 - 收到眼动追踪状态变化请求: $isEnabled")
        lifecycleScope.launch {
            try {
                // 处理眼动追踪状态变化
                if (isEnabled) {
                    Logger.d(TAG, msg = "状态设置为启用，调用启动方法")
                    onStartGazeTracking()
                } else {
                    Logger.d(TAG, msg = "状态设置为禁用，调用停止方法")
                    onStopGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "处理眼动追踪状态变化异常: ${e.message}")
            }
        }
    }

    private fun createWebPrintJob(webView: WebView) {
        val printManager = getSystemService(PRINT_SERVICE) as PrintManager
        val jobName = getString(R.string.app_name) + " Document"

        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        printManager.print(
            jobName, printAdapter,
            PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setColorMode(PrintAttributes.COLOR_MODE_COLOR)
                .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()
        )
    }

    /**
     * 切换全屏
     * @param isFUll true 全屏，false 退出全屏
     */
    private fun toggleFullScreen(isFUll:Boolean){
        if (isFUll){
            //切成全屏
            val constraintSet = ConstraintSet()
            constraintSet.clone(clDetectionRoot)
            constraintSet.clear(R.id.cl_content)
            constraintSet.connect(R.id.cl_content, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.connect(R.id.cl_content, ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT)
            constraintSet.connect(R.id.cl_content, ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT)
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.TOP, 0)
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.END, 0)
            constraintSet.applyTo(clDetectionRoot)

            val params = clContent.layoutParams as LayoutParams
            params.width = LayoutParams.MATCH_PARENT
            params.height = LayoutParams.MATCH_PARENT
            clContent.layoutParams = params

        }else{
            //切成半屏
            val constraintSet = ConstraintSet()
            constraintSet.clone(clDetectionRoot)
            constraintSet.clear(R.id.cl_content)
            constraintSet.connect(R.id.cl_content, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP)
            constraintSet.connect(R.id.cl_content, ConstraintSet.LEFT, R.id.iv_user_info_bg, ConstraintSet.RIGHT)
            constraintSet.connect(R.id.cl_content, ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT)
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.TOP, 90.dp2px(this))
            constraintSet.setMargin(R.id.cl_content, ConstraintSet.END, 20.dp2px(this))
            constraintSet.applyTo(clDetectionRoot)

            val params = clContent.layoutParams as LayoutParams
            params.width = 0
            params.height = 430.dp2px(this)
            clContent.layoutParams = params
        }
    }

    override fun onPause() {
        super.onPause()
        wbHrv.onPause()
        wbHrv.pauseTimers()
    }

    override fun onResume() {
        super.onResume()
        wbHrv.resumeTimers()
        wbHrv.onResume()
    }

    override fun onStart() {
        super.onStart()
        Logger.d(TAG, msg = "🔄 DetectionActivity1 - onStart() 开始")

        // 绑定到独立进程服务
        val intent = Intent(this, GazeTrackService::class.java)
        val result = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT)
        Logger.d(TAG, msg = "绑定独立进程GazeTrackService结果: $result")

        // 注意：开启相机消息现在在 serviceConnection.onServiceConnected() 回调中发送
        // 这样可以确保服务连接完成后立即发送，避免使用 delay 的副作用
    }

    override fun onStop() {
        super.onStop()
        Logger.d(TAG, msg = "🛑 DetectionActivity1 - onStop() 开始")

        // 发送关闭相机消息
        if (mServiceManager != null) {
            Logger.d(TAG, msg = "📷 发送关闭相机消息")
            val cameraMessage = Message.obtain().apply {
                what = GazeConstants.MSG_TURN_OFF_CAMERA
                replyTo = mClientMessage
            }
            mServiceManager?.send(cameraMessage)
            Logger.d(TAG, msg = "✓ 已发送关闭相机消息")
        }

        // 解绑服务连接
        try {
            Logger.d(TAG, msg = "解绑GazeTrackService服务连接")
            unbindService(serviceConnection)
            mServiceManager = null
            Logger.d(TAG, msg = "✓ GazeTrackService服务连接已解绑")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "解绑GazeTrackService服务连接异常: ${e.message}")
        }
    }



    override fun onDestroy() {
        Logger.d(TAG, msg = "📱 Activity onDestroy - 清理眼动追踪资源")
        Logger.d(TAG, msg = "  当前眼动追踪状态: $isGazeTrackingActive")

        // 确保清理眼动追踪资源
        if (isGazeTrackingActive) {
            Logger.d(TAG, msg = "  调用onStopGazeTracking清理眼动追踪资源")
            try {
                // 调用完整的停止流程，确保相机和追踪服务正确停止
                onStopGazeTracking()
                Logger.d(TAG, msg = "  ✓ 眼动追踪资源已清理")
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  清理眼动追踪资源异常: ${e.message}")
                // 如果onStopGazeTracking失败，至少更新本地状态
                isGazeTrackingActive = false
            }
        }
        // 解绑服务连接
        try {
            Logger.d(TAG, msg = "  解绑GazeTrackService服务连接")
            unbindService(serviceConnection)
            mServiceManager = null
            Logger.d(TAG, msg = "  ✓ GazeTrackService服务连接已解绑")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "  解绑GazeTrackService服务连接异常: ${e.message}")
        }

        // 停止GazeTrackService前台服务以释放资源
        try {
            Logger.d(TAG, msg = "  停止GazeTrackService前台服务")
            stopService(Intent(this, GazeTrackService::class.java))
            Logger.d(TAG, msg = "  ✓ GazeTrackService服务已停止")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "  停止GazeTrackService服务异常: ${e.message}")
        }

        wbHrv.clearWebViewCache()
        wbHrv.destroy()
        super.onDestroy()
        Logger.d(TAG, msg = "📱 Activity onDestroy 完成")
    }

}