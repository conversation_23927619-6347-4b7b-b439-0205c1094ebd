<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .component-title { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #2c3e50; }
      .component-text { font-family: Arial, sans-serif; font-size: 10px; fill: #34495e; }
      .ui-layer { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; }
      .business-layer { fill: #e8f6f3; stroke: #27ae60; stroke-width: 2; }
      .service-layer { fill: #fef9e7; stroke: #f39c12; stroke-width: 2; }
      .data-layer { fill: #fdedec; stroke: #e74c3c; stroke-width: 2; }
      .native-layer { fill: #f4f6f7; stroke: #95a5a6; stroke-width: 2; }
      .component { fill: #ffffff; stroke: #bdc3c7; stroke-width: 1; rx: 5; }
      .arrow { stroke: #7f8c8d; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #e67e22; stroke-width: 2; fill: none; stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">MPD眼动追踪系统架构图</text>

  <!-- UI层 -->
  <rect x="50" y="60" width="1300" height="150" class="ui-layer"/>
  <text x="70" y="85" class="layer-title">UI层 (Presentation Layer)</text>
  
  <!-- MainActivity -->
  <rect x="80" y="100" width="120" height="80" class="component"/>
  <text x="140" y="125" text-anchor="middle" class="component-title">MainActivity</text>
  <text x="140" y="140" text-anchor="middle" class="component-text">主界面</text>
  <text x="140" y="155" text-anchor="middle" class="component-text">Fragment管理</text>
  <text x="140" y="170" text-anchor="middle" class="component-text">设备模式切换</text>

  <!-- CalibrationActivity -->
  <rect x="220" y="100" width="120" height="80" class="component"/>
  <text x="280" y="125" text-anchor="middle" class="component-title">CalibrationActivity</text>
  <text x="280" y="140" text-anchor="middle" class="component-text">校准界面</text>
  <text x="280" y="155" text-anchor="middle" class="component-text">姿态校准</text>
  <text x="280" y="170" text-anchor="middle" class="component-text">视觉校准</text>

  <!-- DetectionActivity -->
  <rect x="360" y="100" width="120" height="80" class="component"/>
  <text x="420" y="125" text-anchor="middle" class="component-title">DetectionActivity</text>
  <text x="420" y="140" text-anchor="middle" class="component-text">检测界面</text>
  <text x="420" y="155" text-anchor="middle" class="component-text">眼动检测</text>
  <text x="420" y="170" text-anchor="middle" class="component-text">结果展示</text>

  <!-- Custom Views -->
  <rect x="500" y="100" width="150" height="80" class="component"/>
  <text x="575" y="120" text-anchor="middle" class="component-title">自定义View组件</text>
  <text x="575" y="135" text-anchor="middle" class="component-text">PostureCalibrationView</text>
  <text x="575" y="150" text-anchor="middle" class="component-text">VisualCalibrationView</text>
  <text x="575" y="165" text-anchor="middle" class="component-text">GazeDemoView</text>

  <!-- ViewModels -->
  <rect x="670" y="100" width="120" height="80" class="component"/>
  <text x="730" y="125" text-anchor="middle" class="component-title">ViewModels</text>
  <text x="730" y="140" text-anchor="middle" class="component-text">CalibrationVM</text>
  <text x="730" y="155" text-anchor="middle" class="component-text">DeviceVM</text>
  <text x="730" y="170" text-anchor="middle" class="component-text">UserVM</text>

  <!-- Media Manager -->
  <rect x="810" y="100" width="120" height="80" class="component"/>
  <text x="870" y="125" text-anchor="middle" class="component-title">PlayManager</text>
  <text x="870" y="140" text-anchor="middle" class="component-text">媒体播放</text>
  <text x="870" y="155" text-anchor="middle" class="component-text">ExoPlayer</text>
  <text x="870" y="170" text-anchor="middle" class="component-text">多语言音频</text>

  <!-- Camera Manager -->
  <rect x="950" y="100" width="120" height="80" class="component"/>
  <text x="1010" y="125" text-anchor="middle" class="component-title">GTCameraManager</text>
  <text x="1010" y="140" text-anchor="middle" class="component-text">相机管理</text>
  <text x="1010" y="155" text-anchor="middle" class="component-text">CameraX</text>
  <text x="1010" y="170" text-anchor="middle" class="component-text">图像采集</text>

  <!-- WebSocket Client -->
  <rect x="1090" y="100" width="120" height="80" class="component"/>
  <text x="1150" y="125" text-anchor="middle" class="component-title">WebSocket</text>
  <text x="1150" y="140" text-anchor="middle" class="component-text">实时通信</text>
  <text x="1150" y="155" text-anchor="middle" class="component-text">数据传输</text>
  <text x="1150" y="170" text-anchor="middle" class="component-text">状态同步</text>

  <!-- 业务逻辑层 -->
  <rect x="50" y="240" width="1300" height="180" class="business-layer"/>
  <text x="70" y="265" class="layer-title">业务逻辑层 (Business Logic Layer)</text>

  <!-- GazeTrackingManager -->
  <rect x="80" y="280" width="150" height="100" class="component"/>
  <text x="155" y="305" text-anchor="middle" class="component-title">GazeTrackingManager</text>
  <text x="155" y="320" text-anchor="middle" class="component-text">眼动追踪管理器</text>
  <text x="155" y="335" text-anchor="middle" class="component-text">服务模式控制</text>
  <text x="155" y="350" text-anchor="middle" class="component-text">状态管理</text>
  <text x="155" y="365" text-anchor="middle" class="component-text">配置管理</text>

  <!-- TrackingManager -->
  <rect x="250" y="280" width="150" height="100" class="component"/>
  <text x="325" y="305" text-anchor="middle" class="component-title">TrackingManager</text>
  <text x="325" y="320" text-anchor="middle" class="component-text">图像处理调度</text>
  <text x="325" y="335" text-anchor="middle" class="component-text">模式路由</text>
  <text x="325" y="350" text-anchor="middle" class="component-text">TRACK/CALIBRATION</text>
  <text x="325" y="365" text-anchor="middle" class="component-text">POSTURE_CALIBRATION</text>

  <!-- AppliedManager -->
  <rect x="420" y="280" width="150" height="100" class="component"/>
  <text x="495" y="305" text-anchor="middle" class="component-title">AppliedManager</text>
  <text x="495" y="320" text-anchor="middle" class="component-text">应用管理器</text>
  <text x="495" y="335" text-anchor="middle" class="component-text">治疗模式</text>
  <text x="495" y="350" text-anchor="middle" class="component-text">虚化/阅读/注视</text>
  <text x="495" y="365" text-anchor="middle" class="component-text">跟随/扫视</text>

  <!-- MaskManager -->
  <rect x="590" y="280" width="150" height="100" class="component"/>
  <text x="665" y="305" text-anchor="middle" class="component-title">MaskManager</text>
  <text x="665" y="320" text-anchor="middle" class="component-text">遮盖疗法管理</text>
  <text x="665" y="335" text-anchor="middle" class="component-text">遮盖模式</text>
  <text x="665" y="350" text-anchor="middle" class="component-text">遮盖区域</text>
  <text x="665" y="365" text-anchor="middle" class="component-text">遮盖幅度</text>

  <!-- DeviceManager -->
  <rect x="760" y="280" width="150" height="100" class="component"/>
  <text x="835" y="305" text-anchor="middle" class="component-title">DeviceManager</text>
  <text x="835" y="320" text-anchor="middle" class="component-text">设备管理</text>
  <text x="835" y="335" text-anchor="middle" class="component-text">启动模式</text>
  <text x="835" y="350" text-anchor="middle" class="component-text">设备配置</text>
  <text x="835" y="365" text-anchor="middle" class="component-text">状态监控</text>

  <!-- UserManager -->
  <rect x="930" y="280" width="150" height="100" class="component"/>
  <text x="1005" y="305" text-anchor="middle" class="component-title">UserManager</text>
  <text x="1005" y="320" text-anchor="middle" class="component-text">用户管理</text>
  <text x="1005" y="335" text-anchor="middle" class="component-text">用户信息</text>
  <text x="1005" y="350" text-anchor="middle" class="component-text">检测项目</text>
  <text x="1005" y="365" text-anchor="middle" class="component-text">权限管理</text>

  <!-- FaceDetectorProcessor -->
  <rect x="1100" y="280" width="150" height="100" class="component"/>
  <text x="1175" y="305" text-anchor="middle" class="component-title">FaceDetectorProcessor</text>
  <text x="1175" y="320" text-anchor="middle" class="component-text">人脸检测处理</text>
  <text x="1175" y="335" text-anchor="middle" class="component-text">面部识别</text>
  <text x="1175" y="350" text-anchor="middle" class="component-text">特征提取</text>
  <text x="1175" y="365" text-anchor="middle" class="component-text">质量评估</text>

  <!-- 服务层 -->
  <rect x="50" y="450" width="1300" height="150" class="service-layer"/>
  <text x="70" y="475" class="layer-title">服务层 (Service Layer)</text>

  <!-- GazeTrackService -->
  <rect x="80" y="490" width="200" height="80" class="component"/>
  <text x="180" y="515" text-anchor="middle" class="component-title">GazeTrackService</text>
  <text x="180" y="530" text-anchor="middle" class="component-text">眼动追踪服务</text>
  <text x="180" y="545" text-anchor="middle" class="component-text">后台服务</text>
  <text x="180" y="560" text-anchor="middle" class="component-text">消息处理</text>

  <!-- WebSocketService -->
  <rect x="300" y="490" width="200" height="80" class="component"/>
  <text x="400" y="515" text-anchor="middle" class="component-title">GazeWebSocketService</text>
  <text x="400" y="530" text-anchor="middle" class="component-text">WebSocket服务器</text>
  <text x="400" y="545" text-anchor="middle" class="component-text">实时数据传输</text>
  <text x="400" y="560" text-anchor="middle" class="component-text">端口9200</text>

  <!-- Network Services -->
  <rect x="520" y="490" width="200" height="80" class="component"/>
  <text x="620" y="515" text-anchor="middle" class="component-title">网络服务</text>
  <text x="620" y="530" text-anchor="middle" class="component-text">API接口</text>
  <text x="620" y="545" text-anchor="middle" class="component-text">设备注册</text>
  <text x="620" y="560" text-anchor="middle" class="component-text">数据上传</text>

  <!-- Repository Layer -->
  <rect x="740" y="490" width="200" height="80" class="component"/>
  <text x="840" y="515" text-anchor="middle" class="component-title">Repository层</text>
  <text x="840" y="530" text-anchor="middle" class="component-text">DeviceRepository</text>
  <text x="840" y="545" text-anchor="middle" class="component-text">UserRepository</text>
  <text x="840" y="560" text-anchor="middle" class="component-text">UpdateRepository</text>

  <!-- Configuration -->
  <rect x="960" y="490" width="200" height="80" class="component"/>
  <text x="1060" y="515" text-anchor="middle" class="component-title">配置管理</text>
  <text x="1060" y="530" text-anchor="middle" class="component-text">UrlConfig</text>
  <text x="1060" y="545" text-anchor="middle" class="component-text">环境切换</text>
  <text x="1060" y="560" text-anchor="middle" class="component-text">参数配置</text>

  <!-- 数据层 -->
  <rect x="50" y="630" width="1300" height="150" class="data-layer"/>
  <text x="70" y="655" class="layer-title">数据层 (Data Layer)</text>

  <!-- MMKV -->
  <rect x="80" y="670" width="150" height="80" class="component"/>
  <text x="155" y="695" text-anchor="middle" class="component-title">MMKV</text>
  <text x="155" y="710" text-anchor="middle" class="component-text">本地缓存</text>
  <text x="155" y="725" text-anchor="middle" class="component-text">配置存储</text>
  <text x="155" y="740" text-anchor="middle" class="component-text">用户偏好</text>

  <!-- File System -->
  <rect x="250" y="670" width="150" height="80" class="component"/>
  <text x="325" y="695" text-anchor="middle" class="component-title">文件系统</text>
  <text x="325" y="710" text-anchor="middle" class="component-text">校准参数</text>
  <text x="325" y="725" text-anchor="middle" class="component-text">模型文件</text>
  <text x="325" y="740" text-anchor="middle" class="component-text">日志文件</text>

  <!-- Network API -->
  <rect x="420" y="670" width="150" height="80" class="component"/>
  <text x="495" y="695" text-anchor="middle" class="component-title">网络API</text>
  <text x="495" y="710" text-anchor="middle" class="component-text">REST API</text>
  <text x="495" y="725" text-anchor="middle" class="component-text">设备API</text>
  <text x="495" y="740" text-anchor="middle" class="component-text">用户API</text>

  <!-- Database -->
  <rect x="590" y="670" width="150" height="80" class="component"/>
  <text x="665" y="695" text-anchor="middle" class="component-title">数据库</text>
  <text x="665" y="710" text-anchor="middle" class="component-text">检测记录</text>
  <text x="665" y="725" text-anchor="middle" class="component-text">用户数据</text>
  <text x="665" y="740" text-anchor="middle" class="component-text">设备信息</text>

  <!-- Cloud Storage -->
  <rect x="760" y="670" width="150" height="80" class="component"/>
  <text x="835" y="695" text-anchor="middle" class="component-title">云端存储</text>
  <text x="835" y="710" text-anchor="middle" class="component-text">数据备份</text>
  <text x="835" y="725" text-anchor="middle" class="component-text">模型更新</text>
  <text x="835" y="740" text-anchor="middle" class="component-text">远程配置</text>

  <!-- Logger -->
  <rect x="930" y="670" width="150" height="80" class="component"/>
  <text x="1005" y="695" text-anchor="middle" class="component-title">日志系统</text>
  <text x="1005" y="710" text-anchor="middle" class="component-text">Logger</text>
  <text x="1005" y="725" text-anchor="middle" class="component-text">错误追踪</text>
  <text x="1005" y="740" text-anchor="middle" class="component-text">性能监控</text>

  <!-- LiveEventBus -->
  <rect x="1100" y="670" width="150" height="80" class="component"/>
  <text x="1175" y="695" text-anchor="middle" class="component-title">事件总线</text>
  <text x="1175" y="710" text-anchor="middle" class="component-text">LiveEventBus</text>
  <text x="1175" y="725" text-anchor="middle" class="component-text">组件通信</text>
  <text x="1175" y="740" text-anchor="middle" class="component-text">状态同步</text>

  <!-- Native层 -->
  <rect x="50" y="810" width="1300" height="150" class="native-layer"/>
  <text x="70" y="835" class="layer-title">Native层 (JNI/C++ Layer)</text>

  <!-- GazeService -->
  <rect x="80" y="850" width="180" height="80" class="component"/>
  <text x="170" y="875" text-anchor="middle" class="component-title">GazeService</text>
  <text x="170" y="890" text-anchor="middle" class="component-text">眼动服务核心</text>
  <text x="170" y="905" text-anchor="middle" class="component-text">图像处理</text>
  <text x="170" y="920" text-anchor="middle" class="component-text">算法调度</text>

  <!-- Detection -->
  <rect x="280" y="850" width="120" height="80" class="component"/>
  <text x="340" y="875" text-anchor="middle" class="component-title">Detection</text>
  <text x="340" y="890" text-anchor="middle" class="component-text">人脸检测</text>
  <text x="340" y="905" text-anchor="middle" class="component-text">关键点检测</text>
  <text x="340" y="920" text-anchor="middle" class="component-text">特征提取</text>

  <!-- GazeTracker -->
  <rect x="420" y="850" width="120" height="80" class="component"/>
  <text x="480" y="875" text-anchor="middle" class="component-title">GazeTracker</text>
  <text x="480" y="890" text-anchor="middle" class="component-text">视线追踪</text>
  <text x="480" y="905" text-anchor="middle" class="component-text">注视点计算</text>
  <text x="480" y="920" text-anchor="middle" class="component-text">距离估算</text>

  <!-- GazeCalibrate -->
  <rect x="560" y="850" width="120" height="80" class="component"/>
  <text x="620" y="875" text-anchor="middle" class="component-title">GazeCalibrate</text>
  <text x="620" y="890" text-anchor="middle" class="component-text">眼动校准</text>
  <text x="620" y="905" text-anchor="middle" class="component-text">参数优化</text>
  <text x="620" y="920" text-anchor="middle" class="component-text">精度提升</text>

  <!-- PoseAlign -->
  <rect x="700" y="850" width="120" height="80" class="component"/>
  <text x="760" y="875" text-anchor="middle" class="component-title">PoseAlign</text>
  <text x="760" y="890" text-anchor="middle" class="component-text">姿态校准</text>
  <text x="760" y="905" text-anchor="middle" class="component-text">头部姿态</text>
  <text x="760" y="920" text-anchor="middle" class="component-text">位置矫正</text>

  <!-- GazeApplication -->
  <rect x="840" y="850" width="140" height="80" class="component"/>
  <text x="910" y="875" text-anchor="middle" class="component-title">GazeApplication</text>
  <text x="910" y="890" text-anchor="middle" class="component-text">应用模式</text>
  <text x="910" y="905" text-anchor="middle" class="component-text">治疗算法</text>
  <text x="910" y="920" text-anchor="middle" class="component-text">效果处理</text>

  <!-- SaveVideos -->
  <rect x="1000" y="850" width="120" height="80" class="component"/>
  <text x="1060" y="875" text-anchor="middle" class="component-title">SaveVideos</text>
  <text x="1060" y="890" text-anchor="middle" class="component-text">数据保存</text>
  <text x="1060" y="905" text-anchor="middle" class="component-text">视频录制</text>
  <text x="1060" y="920" text-anchor="middle" class="component-text">文件管理</text>

  <!-- OpenCV -->
  <rect x="1140" y="850" width="120" height="80" class="component"/>
  <text x="1200" y="875" text-anchor="middle" class="component-title">OpenCV</text>
  <text x="1200" y="890" text-anchor="middle" class="component-text">图像处理</text>
  <text x="1200" y="905" text-anchor="middle" class="component-text">计算机视觉</text>
  <text x="1200" y="920" text-anchor="middle" class="component-text">算法库</text>

  <!-- 数据流箭头 -->
  <!-- UI到业务层 -->
  <line x1="140" y1="180" x2="155" y2="280" class="arrow"/>
  <line x1="280" y1="180" x2="325" y2="280" class="arrow"/>
  <line x1="420" y1="180" x2="495" y2="280" class="arrow"/>
  <line x1="730" y1="180" x2="835" y2="280" class="arrow"/>
  <line x1="1010" y1="180" x2="325" y2="280" class="arrow"/>

  <!-- 业务层到服务层 -->
  <line x1="155" y1="380" x2="180" y2="490" class="arrow"/>
  <line x1="325" y1="380" x2="180" y2="490" class="arrow"/>
  <line x1="495" y1="380" x2="400" y2="490" class="arrow"/>
  <line x1="835" y1="380" x2="840" y2="490" class="arrow"/>

  <!-- 服务层到数据层 -->
  <line x1="180" y1="570" x2="155" y2="670" class="arrow"/>
  <line x1="400" y1="570" x2="495" y2="670" class="arrow"/>
  <line x1="620" y1="570" x2="495" y2="670" class="arrow"/>
  <line x1="840" y1="570" x2="665" y2="670" class="arrow"/>

  <!-- 服务层到Native层 -->
  <line x1="180" y1="570" x2="170" y2="850" class="arrow"/>
  <line x1="180" y1="570" x2="340" y2="850" class="arrow"/>
  <line x1="180" y1="570" x2="480" y2="850" class="arrow"/>
  <line x1="180" y1="570" x2="620" y2="850" class="arrow"/>

  <!-- 数据流线 -->
  <line x1="1010" y1="180" x2="325" y2="280" class="data-flow"/>
  <text x="650" y="230" class="component-text">图像数据流</text>

  <line x1="325" y1="380" x2="170" y2="850" class="data-flow"/>
  <text x="250" y="620" class="component-text">处理指令</text>

  <line x1="170" y1="850" x2="325" y2="380" class="data-flow"/>
  <text x="350" y="620" class="component-text">处理结果</text>

</svg>
