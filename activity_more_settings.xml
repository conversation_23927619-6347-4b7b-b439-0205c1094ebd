<?xml version="1.0" encoding="utf-8"?> 
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" 
    xmlns:app="http://schemas.android.com/apk/res-auto" 
    android:layout_width="match_parent" 
    android:layout_height="match_parent" 
    android:orientation="vertical" 
    android:background="@drawable/gradient_pink_background" 
    android:gravity="center" 
    android:padding="16dp"> 
 
    <!-- Card Container --> 
    <androidx.cardview.widget.CardView 
        android:layout_width="697dp" 
        android:layout_height="match_parent" 
        android:layout_gravity="center" 
        android:layout_marginHorizontal="16dp" 
        android:layout_marginVertical="32dp"
        app:cardCornerRadius="12dp" 
        app:cardElevation="8dp"> 
 
        <LinearLayout 
            android:layout_width="match_parent" 
            android:layout_height="match_parent" 
            android:background="@android:color/white" 
            android:orientation="vertical"> 
 
            <!-- 标题栏 --> 
            <LinearLayout 
                android:layout_width="match_parent" 
                android:layout_height="wrap_content" 
                android:background="@android:color/white" 
                android:elevation="2dp" 
                android:gravity="center_vertical" 
                android:orientation="horizontal" 
                android:padding="16dp"> 
 
                <ImageView 
                    android:id="@+id/iv_back" 
                    android:layout_width="24dp" 
                    android:layout_height="24dp" 
                    android:background="?android:attr/selectableItemBackgroundBorderless" 
                    android:padding="4dp" 
                    android:src="@drawable/icon_back_black_fine" 
                    android:tint="@color/color_666666" /> 
 
                <TextView 
                    android:layout_width="0dp" 
                    android:layout_height="wrap_content" 
                    android:layout_weight="1" 
                    android:gravity="center" 
                    android:text="@string/str_more_settings" 
                    android:textColor="@color/color_333333" 
                    android:textSize="18sp" 
                    android:textStyle="bold" /> 
 
                <View 
                    android:layout_width="24dp" 
                    android:layout_height="24dp" /> 
 
            </LinearLayout> 
 
            <!-- Divider --> 
            <View 
                android:layout_width="match_parent" 
                android:layout_height="1dp" 
                android:background="@color/color_cccccc" /> 
 
            <!-- Content Container with ScrollView --> 
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fillViewport="true">
                
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/white"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 首页语音引导 -->
                    <LinearLayout
                        android:id="@+id/ll_proactively_greet"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:minHeight="60dp">

                        <TextView
                            android:id="@+id/tv_proactively_greet"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:includeFontPadding="false"
                            android:text="首页语音引导"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_proactively_greet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:thumb="@drawable/switch_mask_therapy_thumb"
                            android:thumbTint="@color/white"
                            android:trackTint="@color/selector_common_switch_compat_track_tint"
                            app:thumbTint="@color/white"
                            app:track="@drawable/switch_mask_therapy_style"
                            app:trackTint="@color/selector_common_switch_compat_track_tint" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_eeeeee"
                        android:layout_marginVertical="4dp" />

                    <!-- 模型版本 -->
                    <LinearLayout
                        android:id="@+id/ll_model_version"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:minHeight="60dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="模型版本: "
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/tv_model_version"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="V1.0.0.1"
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/btn_update_model"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/rounded_pink_button"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="8dp"
                            android:text="@string/str_update"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_eeeeee"
                        android:layout_marginVertical="4dp" />

                    <!-- 测试集版本 -->
                    <LinearLayout
                        android:id="@+id/ll_test_version"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:minHeight="60dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="测试集版本: "
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/tv_test_version"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="V2.0.1"
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/btn_update_test"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/rounded_pink_button"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="8dp"
                            android:text="@string/str_update"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_eeeeee"
                        android:layout_marginVertical="4dp" />

                    <!-- 数据缓存 -->
                    <LinearLayout
                        android:id="@+id/ll_data_cache"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:minHeight="60dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="数据缓存: "
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/tv_data_cache_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="无"
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/btn_upload_cache"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/rounded_pink_button"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="8dp"
                            android:text="@string/str_upload"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_eeeeee"
                        android:layout_marginVertical="4dp" />

                    <!-- 指尖式数据采集 -->
                    <LinearLayout
                        android:id="@+id/ll_fingertip_collection"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:minHeight="60dp">

                        <TextView
                            android:id="@+id/tv_fingertip_collection"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:includeFontPadding="false"
                            android:text="@string/str_fingertip_data_collection"
                            android:textColor="@color/color_333333"
                            android:textSize="16sp" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switch_fingertip_collection"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:thumb="@drawable/switch_mask_therapy_thumb"
                            android:thumbTint="@color/white"
                            android:trackTint="@color/selector_common_switch_compat_track_tint"
                            app:thumbTint="@color/white"
                            app:track="@drawable/switch_mask_therapy_style"
                            app:trackTint="@color/selector_common_switch_compat_track_tint" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_eeeeee"
                        android:layout_marginVertical="4dp" />

                    <!-- 采集器编号 -->
                    <LinearLayout
                        android:id="@+id/ll_collector_number"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:minHeight="60dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="采集器编号: "
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/tv_collector_number"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:includeFontPadding="false"
                                android:text="000477"
                                android:textColor="@color/color_333333"
                                android:textSize="16sp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/btn_change_collector"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/rounded_pink_button"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="8dp"
                            android:text="@string/str_change"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </ScrollView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
