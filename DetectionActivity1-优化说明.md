# DetectionActivity1 跨进程消息通信优化说明

## 优化概述

将DetectionActivity1中的眼动追踪控制从直接调用GazeTrackingManager改为通过跨进程消息通信的方式与GazeTrackService交互，实现更好的解耦和控制。

## 主要改动

### 1. DetectionActivity1 改动

#### 1.1 消息处理优化
- **parseMessage方法**: 新增对`MSG_GAZE_TRACKING_STATE`消息的处理，接收服务端的状态回调
- **状态同步**: 根据服务端返回的状态更新本地`isGazeTrackingActive`状态并通知Web页面

#### 1.2 启动眼动追踪流程优化 (onStartGazeTracking)
**原流程**: 直接调用GazeTrackingManager.startGazeTracking()
**新流程**: 
1. 检查服务连接状态
2. 发送`MSG_TURN_ON_CAMERA`消息启动相机
3. 延迟1秒后发送`MSG_START_TRACK`消息开始追踪
4. 暂时设置本地状态，等待服务端确认

#### 1.3 停止眼动追踪流程优化 (onStopGazeTracking)
**原流程**: 直接调用GazeTrackingManager.stopGazeTracking()
**新流程**:
1. 检查服务连接状态
2. 发送`MSG_STOP_TRACK`消息停止追踪
3. 延迟0.5秒后发送`MSG_TURN_OFF_CAMERA`消息关闭相机
4. 更新本地状态并通知Web页面

#### 1.4 生命周期管理优化
- **onPause**: 通过消息通信暂停追踪和关闭相机
- **onResume**: 通过消息通信恢复相机和追踪（如果之前处于活跃状态）

### 2. GazeTrackService 改动

#### 2.1 服务启动优化
- **移除自动启动**: 在onCreate中移除了自动调用`startGazeTrackingFlow()`
- **按需启动**: 改为等待客户端消息来控制相机和追踪的启动

#### 2.2 相机控制优化 (MSG_TURN_ON_CAMERA)
**新增功能**:
1. 确保模型和配置文件已拷贝
2. 设置摄像头监听器
3. 启动摄像头
4. 异步处理，避免阻塞

#### 2.3 追踪控制优化 (MSG_START_TRACK)
**优化功能**:
1. 设置TrackingManager的生命周期和监听器
2. 加载校准参数
3. 启动追踪算法
4. 发送状态回调给客户端（成功/失败）

## 消息通信流程

### 启动流程
```
DetectionActivity1                    GazeTrackService
      |                                     |
      |---> MSG_TURN_ON_CAMERA ----------->|
      |                                     |---> 拷贝模型文件
      |                                     |---> 设置相机监听器
      |                                     |---> 启动相机
      |                                     |
      |---> MSG_START_TRACK -------------->|
      |                                     |---> 设置追踪监听器
      |                                     |---> 加载校准参数
      |                                     |---> 启动追踪算法
      |                                     |
      |<--- MSG_GAZE_TRACKING_STATE -------|
      |                                     |
      |---> 更新UI状态                      |
```

### 停止流程
```
DetectionActivity1                    GazeTrackService
      |                                     |
      |---> MSG_STOP_TRACK --------------->|
      |                                     |---> 停止追踪算法
      |                                     |---> 移除UI组件
      |                                     |
      |<--- MSG_GAZE_TRACKING_STATE -------|
      |                                     |
      |---> MSG_TURN_OFF_CAMERA ---------->|
      |                                     |---> 关闭相机
      |                                     |
      |---> 更新UI状态                      |
```

## 优势

1. **解耦**: Activity和Service通过消息通信，降低耦合度
2. **可控性**: 可以精确控制相机和追踪的启动时机
3. **状态同步**: 通过回调消息确保状态一致性
4. **错误处理**: 更好的异常处理和状态反馈
5. **资源管理**: 更精确的资源控制，避免不必要的资源占用

## 注意事项

1. **时序控制**: 相机启动和追踪启动之间需要适当的延迟
2. **状态管理**: 需要正确处理各种异常情况下的状态同步
3. **生命周期**: Activity的生命周期变化需要正确处理服务状态
4. **消息队列**: 确保消息发送的顺序和时机正确

## 测试建议

1. 测试正常启动/停止流程
2. 测试Activity生命周期变化时的状态处理
3. 测试异常情况下的状态恢复
4. 测试服务连接断开时的处理
5. 测试快速启动/停止操作的稳定性
