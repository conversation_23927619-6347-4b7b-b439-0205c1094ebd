# 眼动追踪AAR封装架构方案

## 1. 项目概述

基于现有的眼动追踪代码，将其封装成独立的AAR库，提供给第三方应用集成使用。

## 2. 核心组件分析

### 2.1 现有架构组件

#### JNI层
- **GazeTrack.kt**: JNI桥接层，与C++层通信
- **native-lib.cpp**: C++入口文件
- **GazeService.cpp/h**: 核心眼动服务
- **GazeApplication.cpp/h**: 眼动应用管理

#### 服务层
- **GazeTrackService.kt**: 独立进程的核心服务
- **TrackingManager.kt**: 追踪管理器
- **GazeTrackingManager.kt**: 眼动追踪统一管理器

#### 相机层
- **GTCameraManager.kt**: 相机管理，支持4048x3040分辨率
- **ICameraListener.kt**: 相机回调接口

#### 校准层
- **CalibrationActivity.kt**: 校准界面
- **PostureCalibrationView.kt**: 姿势校准
- **VisualCalibrationView.kt**: 视标校准

#### 数据模型
- **GazeTrackResult.kt**: 眼动追踪结果
- **CalibrationResult.kt**: 校准结果
- **ServiceMode.kt**: 服务模式枚举

## 3. AAR封装方案

### 3.1 模块结构

```
gaze-tracking-sdk/
├── src/main/
│   ├── java/com/airdoc/gaze/
│   │   ├── api/                    # 公开API
│   │   │   ├── GazeTrackingSDK.kt
│   │   │   └── IGazeTrackListener.kt
│   │   ├── core/                   # 核心服务
│   │   │   ├── GazeTrackService.kt
│   │   │   ├── TrackingManager.kt
│   │   │   └── GazeTrackingManager.kt
│   │   ├── jni/                    # JNI层
│   │   │   └── GazeTrack.kt
│   │   ├── camera/                 # 相机管理
│   │   │   ├── GTCameraManager.kt
│   │   │   └── ICameraListener.kt
│   │   ├── calibration/            # 校准系统
│   │   │   ├── CalibrationActivity.kt
│   │   │   ├── PostureCalibrationView.kt
│   │   │   └── VisualCalibrationView.kt
│   │   ├── model/                  # 数据模型
│   │   │   ├── GazeTrackResult.kt
│   │   │   ├── CalibrationResult.kt
│   │   │   └── enums/
│   │   └── utils/                  # 工具类
│   │       ├── GTUtils.kt
│   │       └── WebSocketService.kt
│   ├── cpp/                        # Native代码
│   │   ├── native-lib.cpp
│   │   ├── GazeService.cpp/h
│   │   ├── GazeApplication.cpp/h
│   │   └── include/                # 第三方库头文件
│   ├── assets/                     # 模型文件
│   │   ├── models/
│   │   └── config/
│   └── res/                        # 资源文件
│       ├── layout/
│       ├── values/
│       └── drawable/
└── build.gradle.kts
```

### 3.2 公开API设计

#### 主要API类
```kotlin
class GazeTrackingSDK {
    companion object {
        fun init(context: Context, config: GazeConfig): Boolean
        fun startTracking(listener: IGazeTrackListener): Boolean
        fun stopTracking(): Boolean
        fun startCalibration(mode: CalibrationMode, activity: Activity): Boolean
        fun isTracking(): Boolean
        fun release()
    }
}
```

#### 监听器接口
```kotlin
interface IGazeTrackListener {
    fun onGazeTracking(result: GazeTrackResult)
    fun onCalibrationComplete(result: CalibrationResult)
    fun onError(error: GazeError)
    fun onServiceModeChange(mode: ServiceMode)
}
```

### 3.3 依赖管理

#### 内部依赖
- CameraX (相机框架)
- OpenCV (图像处理)
- Kotlin协程
- LiveEventBus (事件总线)

#### 外部依赖
- AI模型文件 (.rknn格式)
- 校准参数文件
- 配置文件

## 4. 构建配置

### 4.1 build.gradle.kts
```kotlin
plugins {
    id("com.android.library")
    id("kotlin-android")
    id("kotlin-parcelize")
}

android {
    namespace = "com.airdoc.gaze"
    compileSdk = 34
    
    defaultConfig {
        minSdk = 29
        targetSdk = 34
        
        externalNativeBuild {
            cmake {
                cppFlags("-std=c++14")
                arguments("-DANDROID_STL=c++_shared")
                abiFilters("arm64-v8a")
            }
        }
    }
    
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles("proguard-rules.pro")
        }
    }
    
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
        }
    }
}
```

### 4.2 ProGuard规则
```proguard
# 保护JNI方法
-keepclassmembers class com.airdoc.gaze.jni.GazeTrack {
    native <methods>;
}

# 保护回调类
-keep class com.airdoc.gaze.jni.GazeTrack$NativeGazeTrackCallback {
    public void onGazeTracking(java.util.HashMap);
    public void onCalibrating(java.util.HashMap);
}

# 保护数据模型
-keep class com.airdoc.gaze.model.** { *; }
-keep class com.airdoc.gaze.model.enums.** { *; }
```

## 5. 集成使用示例

### 5.1 初始化
```kotlin
class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val config = GazeConfig.Builder()
            .setModelPath("models/")
            .setConfigPath("config/")
            .build()
            
        GazeTrackingSDK.init(this, config)
    }
}
```

### 5.2 开始追踪
```kotlin
private val gazeListener = object : IGazeTrackListener {
    override fun onGazeTracking(result: GazeTrackResult) {
        // 处理眼动追踪结果
        Log.d("Gaze", "x: ${result.x}, y: ${result.y}")
    }
    
    override fun onError(error: GazeError) {
        // 处理错误
    }
}

// 开始追踪
GazeTrackingSDK.startTracking(gazeListener)
```

## 6. 发布配置

### 6.1 Maven发布
```kotlin
publishing {
    publications {
        create<MavenPublication>("release") {
            from(components["release"])
            groupId = "com.airdoc"
            artifactId = "gaze-tracking-sdk"
            version = "1.0.0"
        }
    }
}
```

## 7. 注意事项

1. **权限要求**: 需要相机权限
2. **硬件要求**: 支持4048x3040分辨率的后置摄像头
3. **系统要求**: Android API 29+
4. **架构支持**: arm64-v8a
5. **模型文件**: 需要正确配置AI模型路径
6. **独立进程**: 服务运行在独立进程中，需要正确处理进程间通信
