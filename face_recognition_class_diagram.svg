<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .class-box { fill: #f0f8ff; stroke: #4682b4; stroke-width: 2; }
      .class-title { fill: #2e4057; font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; }
      .class-method { fill: #333; font-family: Arial, sans-serif; font-size: 11px; }
      .class-field { fill: #666; font-family: Arial, sans-serif; font-size: 10px; }
      .arrow { stroke: #4682b4; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dependency { stroke: #888; stroke-width: 1; stroke-dasharray: 5,5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4682b4" />
    </marker>
  </defs>
  
  <!-- MainActivity -->
  <rect x="50" y="50" width="300" height="280" class="class-box"/>
  <text x="200" y="75" text-anchor="middle" class="class-title">MainActivity</text>
  <line x1="60" y1="85" x2="340" y2="85" stroke="#4682b4" stroke-width="1"/>
  
  <text x="60" y="105" class="class-field">- faceDetector: FaceDetectorProcessor</text>
  <text x="60" y="120" class="class-field">- cameraXVM: CameraXViewModel</text>
  <text x="60" y="135" class="class-field">- cameraProvider: ProcessCameraProvider</text>
  <text x="60" y="150" class="class-field">- camera: Camera</text>
  <text x="60" y="165" class="class-field">- imageAnalysis: ImageAnalysis</text>
  <text x="60" y="180" class="class-field">- detectedFaceStartTime: long</text>
  <text x="60" y="195" class="class-field">- noDetectedFaceStartTime: long</text>
  <text x="60" y="210" class="class-field">- isPlayedSpeechGuide: boolean</text>
  <text x="60" y="225" class="class-field">- focalLengthPx: float</text>
  
  <line x1="60" y1="235" x2="340" y2="235" stroke="#4682b4" stroke-width="1"/>
  <text x="60" y="250" class="class-method">+ onCreate()</text>
  <text x="60" y="265" class="class-method">+ onStart()</text>
  <text x="60" y="280" class="class-method">+ onResume()</text>
  <text x="60" y="295" class="class-method">- bindAllCameraUseCases()</text>
  <text x="60" y="310" class="class-method">- handleImageProxy(ImageProxy)</text>
  <text x="60" y="325" class="class-method">- playSpeechGuide()</text>

  <!-- FaceDetectorProcessor -->
  <rect x="450" y="50" width="280" height="200" class="class-box"/>
  <text x="590" y="75" text-anchor="middle" class="class-title">FaceDetectorProcessor</text>
  <line x1="460" y1="85" x2="720" y2="85" stroke="#4682b4" stroke-width="1"/>
  
  <text x="460" y="105" class="class-field">- realTimeOpts: FaceDetectorOptions</text>
  <text x="460" y="120" class="class-field">- detector: FaceDetector</text>
  
  <line x1="460" y1="130" x2="720" y2="130" stroke="#4682b4" stroke-width="1"/>
  <text x="460" y="150" class="class-method">+ detectInImage(...)</text>
  <text x="460" y="165" class="class-method">+ isFrontFace(Face): boolean</text>
  <text x="460" y="180" class="class-method">+ distanceCm(Face, float): float</text>
  <text x="460" y="195" class="class-method">+ calculateFocalLength(...): float</text>
  <text x="460" y="210" class="class-method">+ onDestroy()</text>
  <text x="460" y="225" class="class-method">+ calculateFocalLength(...): float</text>
  <text x="460" y="240" class="class-method">+ onDestroy()</text>

  <!-- CameraXViewModel -->
  <rect x="50" y="380" width="280" height="120" class="class-box"/>
  <text x="190" y="405" text-anchor="middle" class="class-title">CameraXViewModel</text>
  <line x1="60" y1="415" x2="320" y2="415" stroke="#4682b4" stroke-width="1"/>
  
  <text x="60" y="435" class="class-field">+ cameraProviderLiveData: MutableLiveData</text>
  
  <line x1="60" y1="445" x2="320" y2="445" stroke="#4682b4" stroke-width="1"/>
  <text x="60" y="465" class="class-method">+ getProcessCameraProvider(Context)</text>

  <!-- CommonPreference -->
  <rect x="380" y="380" width="250" height="120" class="class-box"/>
  <text x="505" y="405" text-anchor="middle" class="class-title">CommonPreference</text>
  <line x1="390" y1="415" x2="620" y2="415" stroke="#4682b4" stroke-width="1"/>
  
  <text x="390" y="435" class="class-field">+ ENABLE_PROACTIVE_GREETING: boolean</text>
  
  <line x1="390" y1="445" x2="620" y2="445" stroke="#4682b4" stroke-width="1"/>
  <text x="390" y="465" class="class-method">+ getNameSpace(): String</text>
  <text x="390" y="480" class="class-method">+ getDefaultValue(): Any</text>

  <!-- PlayManager -->
  <rect x="680" y="380" width="200" height="100" class="class-box"/>
  <text x="780" y="405" text-anchor="middle" class="class-title">PlayManager</text>
  <line x1="690" y1="415" x2="870" y2="415" stroke="#4682b4" stroke-width="1"/>
  <text x="690" y="435" class="class-method">+ playMediaSource(MediaSource)</text>

  <!-- RawMedia -->
  <rect x="50" y="550" width="200" height="100" class="class-box"/>
  <text x="150" y="575" text-anchor="middle" class="class-title">RawMedia</text>
  <line x1="60" y1="585" x2="240" y2="585" stroke="#4682b4" stroke-width="1"/>
  <text x="60" y="605" class="class-method">+ createMediaSource(Context)</text>

  <!-- DeviceManager -->
  <rect x="300" y="550" width="200" height="100" class="class-box"/>
  <text x="400" y="575" text-anchor="middle" class="class-title">DeviceManager</text>
  <line x1="310" y1="585" x2="490" y2="585" stroke="#4682b4" stroke-width="1"/>
  <text x="310" y="605" class="class-method">+ getStartupMode(): StartupMode</text>

  <!-- Google ML Kit -->
  <rect x="800" y="50" width="150" height="80" class="class-box"/>
  <text x="875" y="75" text-anchor="middle" class="class-title">Google ML Kit</text>
  <line x1="810" y1="85" x2="940" y2="85" stroke="#4682b4" stroke-width="1"/>
  <text x="810" y="105" class="class-method">+ process(InputImage)</text>

  <!-- Relationships -->
  <line x1="350" y1="150" x2="450" y2="150" class="arrow"/>
  <text x="380" y="145" class="class-method" font-size="10px">uses</text>

  <line x1="200" y1="330" x2="190" y2="380" class="arrow"/>
  <text x="160" y="360" class="class-method" font-size="10px">uses</text>

  <line x1="300" y1="280" x2="450" y2="420" class="dependency"/>
  <text x="350" y="350" class="class-method" font-size="10px">checks</text>

  <line x1="300" y1="300" x2="720" y2="420" class="dependency"/>
  <text x="500" y="360" class="class-method" font-size="10px">uses</text>

  <line x1="200" y1="330" x2="150" y2="550" class="dependency"/>
  <text x="120" y="440" class="class-method" font-size="10px">creates</text>

  <line x1="250" y1="330" x2="350" y2="550" class="dependency"/>
  <text x="280" y="440" class="class-method" font-size="10px">queries</text>

  <line x1="720" y1="150" x2="800" y2="90" class="dependency"/>
  <text x="740" y="120" class="class-method" font-size="10px">uses</text>

  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="class-title" font-size="18px">人脸识别自动打招呼 - 类图</text>
</svg>