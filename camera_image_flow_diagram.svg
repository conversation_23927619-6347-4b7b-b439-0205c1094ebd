<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .process-box { fill: #e6f3ff; stroke: #4682b4; stroke-width: 2; }
      .data-box { fill: #fff2e6; stroke: #ff8c00; stroke-width: 2; }
      .decision-box { fill: #f0fff0; stroke: #32cd32; stroke-width: 2; }
      .arrow { stroke: #4682b4; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #ff8c00; stroke-width: 2; fill: none; marker-end: url(#arrowhead-data); }
      .text { fill: #2e4057; font-family: Arial, sans-serif; font-size: 11px; }
      .title { fill: #2e4057; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }
      .code-text { fill: #333; font-family: 'Courier New', monospace; font-size: 10px; }
      .highlight { fill: #ff6b6b; font-weight: bold; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4682b4" />
    </marker>
    <marker id="arrowhead-data" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff8c00" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title">CameraX 视频图片传递到 handleImageProxy 的流程</text>
  
  <!-- 1. Camera Hardware -->
  <rect x="50" y="70" width="150" height="60" class="data-box"/>
  <text x="125" y="95" text-anchor="middle" class="text" font-weight="bold">摄像头硬件</text>
  <text x="125" y="110" text-anchor="middle" class="text">连续捕获视频帧</text>
  
  <!-- 2. CameraX Framework -->
  <rect x="250" y="70" width="150" height="60" class="process-box"/>
  <text x="325" y="95" text-anchor="middle" class="text" font-weight="bold">CameraX 框架</text>
  <text x="325" y="110" text-anchor="middle" class="text">管理摄像头生命周期</text>
  
  <!-- 3. ImageAnalysis UseCase -->
  <rect x="450" y="70" width="150" height="60" class="process-box"/>
  <text x="525" y="95" text-anchor="middle" class="text" font-weight="bold">ImageAnalysis</text>
  <text x="525" y="110" text-anchor="middle" class="text">图像分析用例</text>
  
  <!-- Code block 1: ImageAnalysis creation -->
  <rect x="50" y="170" width="550" height="80" class="data-box" fill="#f8f8f8"/>
  <text x="60" y="190" class="code-text">// 1. 创建 ImageAnalysis 实例</text>
  <text x="60" y="205" class="code-text">private val imageAnalysis = ImageAnalysis.Builder()</text>
  <text x="60" y="220" class="code-text">    .setResolutionSelector(resolutionSelector)</text>
  <text x="60" y="235" class="code-text">    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)</text>
  <text x="60" y="250" class="code-text">    .build()</text>
  
  <!-- 4. Analyzer Setup -->
  <rect x="650" y="170" width="200" height="80" class="process-box"/>
  <text x="750" y="195" text-anchor="middle" class="text" font-weight="bold">设置分析器</text>
  <text x="750" y="210" text-anchor="middle" class="text">imageAnalysis.setAnalyzer()</text>
  <text x="750" y="225" text-anchor="middle" class="text">绑定回调函数</text>
  
  <!-- Code block 2: Analyzer setup -->
  <rect x="50" y="290" width="800" height="60" class="data-box" fill="#f8f8f8"/>
  <text x="60" y="310" class="code-text">// 2. 设置图像分析器，关键步骤！</text>
  <text x="60" y="325" class="code-text">imageAnalysis.setAnalyzer(ContextCompat.getMainExecutor(this)) { image -></text>
  <text x="60" y="340" class="code-text highlight">    handleImageProxy(image)  // 这里就是回调入口！</text>
  <text x="60" y="355" class="code-text">}</text>
  
  <!-- 5. Camera Binding -->
  <rect x="50" y="390" width="200" height="60" class="process-box"/>
  <text x="150" y="415" text-anchor="middle" class="text" font-weight="bold">绑定到生命周期</text>
  <text x="150" y="430" text-anchor="middle" class="text">bindToLifecycle()</text>
  
  <!-- Code block 3: Camera binding -->
  <rect x="300" y="390" width="550" height="60" class="data-box" fill="#f8f8f8"/>
  <text x="310" y="410" class="code-text">// 3. 将 ImageAnalysis 绑定到摄像头</text>
  <text x="310" y="425" class="code-text">camera = cameraProvider.bindToLifecycle(this, cameraSelector, imageAnalysis)</text>
  
  <!-- 6. Continuous Frame Processing -->
  <rect x="50" y="490" width="300" height="80" class="decision-box"/>
  <text x="200" y="515" text-anchor="middle" class="text" font-weight="bold">连续帧处理循环</text>
  <text x="200" y="530" text-anchor="middle" class="text">每一帧视频都会触发</text>
  <text x="200" y="545" text-anchor="middle" class="text">分析器回调</text>
  
  <!-- 7. ImageProxy Creation -->
  <rect x="400" y="490" width="200" height="80" class="data-box"/>
  <text x="500" y="515" text-anchor="middle" class="text" font-weight="bold">ImageProxy 对象</text>
  <text x="500" y="530" text-anchor="middle" class="text">包装当前帧数据</text>
  <text x="500" y="545" text-anchor="middle" class="text">+ 元数据信息</text>
  
  <!-- 8. handleImageProxy Method -->
  <rect x="650" y="490" width="200" height="80" class="process-box" fill="#ffeeee"/>
  <text x="750" y="515" text-anchor="middle" class="text highlight" font-weight="bold">handleImageProxy()</text>
  <text x="750" y="530" text-anchor="middle" class="text highlight">目标方法被调用！</text>
  <text x="750" y="545" text-anchor="middle" class="text">处理图像数据</text>
  
  <!-- Code block 4: handleImageProxy method -->
  <rect x="50" y="610" width="800" height="120" class="data-box" fill="#f8f8f8"/>
  <text x="60" y="630" class="code-text">// 4. handleImageProxy 方法实现</text>
  <text x="60" y="645" class="code-text">@OptIn(ExperimentalGetImage::class)</text>
  <text x="60" y="660" class="code-text">private fun handleImageProxy(imageProxy: ImageProxy) {</text>
  <text x="60" y="675" class="code-text">    val mediaImage = imageProxy.image  // 获取实际图像数据</text>
  <text x="60" y="690" class="code-text">    if (mediaImage != null) {</text>
  <text x="60" y="705" class="code-text">        val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)</text>
  <text x="60" y="720" class="code-text">        faceDetector.detectInImage(image, ...)</text>
  <text x="60" y="735" class="code-text">    }</text>
  
  <!-- 9. Face Detection -->
  <rect x="50" y="770" width="200" height="60" class="process-box"/>
  <text x="150" y="795" text-anchor="middle" class="text" font-weight="bold">人脸检测处理</text>
  <text x="150" y="810" text-anchor="middle" class="text">ML Kit 分析</text>
  
  <!-- 10. Resource Cleanup -->
  <rect x="300" y="770" width="200" height="60" class="process-box"/>
  <text x="400" y="795" text-anchor="middle" class="text" font-weight="bold">资源清理</text>
  <text x="400" y="810" text-anchor="middle" class="text">imageProxy.close()</text>
  
  <!-- 11. Next Frame -->
  <rect x="550" y="770" width="200" height="60" class="decision-box"/>
  <text x="650" y="795" text-anchor="middle" class="text" font-weight="bold">等待下一帧</text>
  <text x="650" y="810" text-anchor="middle" class="text">循环继续</text>
  
  <!-- Arrows -->
  <line x1="200" y1="100" x2="250" y2="100" class="data-arrow"/>
  <line x1="400" y1="100" x2="450" y2="100" class="arrow"/>
  <line x1="525" y1="130" x2="525" y2="170" class="arrow"/>
  <line x1="600" y1="210" x2="650" y2="210" class="arrow"/>
  <line x1="750" y1="250" x2="750" y2="290" class="arrow"/>
  <line x1="450" y1="320" x2="450" y2="390" class="arrow"/>
  <line x1="150" y1="450" x2="150" y2="490" class="arrow"/>
  <line x1="350" y1="530" x2="400" y2="530" class="data-arrow"/>
  <line x1="600" y1="530" x2="650" y2="530" class="arrow"/>
  <line x1="750" y1="570" x2="750" y2="610" class="arrow"/>
  <line x1="450" y1="730" x2="450" y2="770" class="arrow"/>
  <line x1="250" y1="800" x2="300" y2="800" class="arrow"/>
  <line x1="500" y1="800" x2="550" y2="800" class="arrow"/>
  
  <!-- Loop back arrow -->
  <path d="M 650 770 Q 900 750 900 530 Q 900 300 200 300" stroke="#32cd32" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  <text x="900" y="540" class="text" fill="#32cd32">持续循环</text>
  
  <!-- Key points -->
  <rect x="900" y="70" width="250" height="200" class="data-box" fill="#fffacd"/>
  <text x="1025" y="90" text-anchor="middle" class="text" font-weight="bold">关键要点</text>
  <text x="910" y="110" class="text">1. ImageAnalysis.setAnalyzer() 是</text>
  <text x="910" y="125" class="text">   关键的绑定步骤</text>
  <text x="910" y="145" class="text">2. Lambda 表达式 { image -> }</text>
  <text x="910" y="160" class="text">   定义了回调函数</text>
  <text x="910" y="180" class="text">3. CameraX 框架自动调用</text>
  <text x="910" y="195" class="text">   handleImageProxy(image)</text>
  <text x="910" y="215" class="text">4. 每一帧视频都会触发</text>
  <text x="910" y="230" class="text">   一次方法调用</text>
  <text x="910" y="250" class="text">5. STRATEGY_KEEP_ONLY_LATEST</text>
  <text x="910" y="265" class="text">   确保处理最新帧</text>
</svg>