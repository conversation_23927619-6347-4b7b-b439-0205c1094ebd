<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; fill: #34495e; }
      .label { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; fill: #7f8c8d; }
      .arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .bidirectional { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); marker-start: url(#arrowhead); }
      .event-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      
      .activity { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .viewmodel { fill: #e67e22; stroke: #d35400; stroke-width: 2; }
      .repository { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .manager { fill: #8e44ad; stroke: #7d3c98; stroke-width: 2; }
      .service { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .storage { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .eventbus { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .network { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="900" fill="#ecf0f1"/>
  
  <!-- 标题 -->
  <text x="700" y="30" class="title">MPD项目无依赖注入架构 - 组件间通信方式</text>
  
  <!-- 左侧：传统MVVM模式 -->
  <text x="350" y="70" class="subtitle">传统MVVM + Manager模式</text>
  
  <!-- Activity层 -->
  <rect x="50" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="110" y="125" class="label">DetectionActivity</text>
  <text x="110" y="140" class="small-text">UI层</text>
  
  <rect x="200" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="260" y="125" class="label">MainActivity</text>
  <text x="260" y="140" class="small-text">主界面</text>
  
  <rect x="350" y="100" width="120" height="60" class="activity" rx="5"/>
  <text x="410" y="125" class="label">ConfigActivity</text>
  <text x="410" y="140" class="small-text">配置界面</text>
  
  <!-- ViewModel层 -->
  <rect x="50" y="200" width="120" height="60" class="viewmodel" rx="5"/>
  <text x="110" y="225" class="label">UserViewModel</text>
  <text x="110" y="240" class="small-text">用户数据</text>
  
  <rect x="200" y="200" width="120" height="60" class="viewmodel" rx="5"/>
  <text x="260" y="225" class="label">DeviceViewModel</text>
  <text x="260" y="240" class="small-text">设备数据</text>
  
  <rect x="350" y="200" width="120" height="60" class="viewmodel" rx="5"/>
  <text x="410" y="225" class="label">UpdateViewModel</text>
  <text x="410" y="240" class="small-text">更新数据</text>
  
  <!-- Repository层 -->
  <rect x="50" y="300" width="120" height="60" class="repository" rx="5"/>
  <text x="110" y="325" class="label">UserRepository</text>
  <text x="110" y="340" class="small-text">用户数据源</text>
  
  <rect x="200" y="300" width="120" height="60" class="repository" rx="5"/>
  <text x="260" y="325" class="label">DeviceRepository</text>
  <text x="260" y="340" class="small-text">设备数据源</text>
  
  <rect x="350" y="300" width="120" height="60" class="repository" rx="5"/>
  <text x="410" y="325" class="label">UpdateRepository</text>
  <text x="410" y="340" class="small-text">更新数据源</text>
  
  <!-- 网络层 -->
  <rect x="125" y="400" width="120" height="60" class="network" rx="5"/>
  <text x="185" y="425" class="label">MpdRetrofitClient</text>
  <text x="185" y="440" class="small-text">网络请求</text>
  
  <rect x="275" y="400" width="120" height="60" class="network" rx="5"/>
  <text x="335" y="425" class="label">ApiService</text>
  <text x="335" y="440" class="small-text">接口定义</text>
  
  <!-- 右侧：Manager + EventBus模式 -->
  <text x="1050" y="70" class="subtitle">Manager单例 + LiveEventBus模式</text>
  
  <!-- Manager层 -->
  <rect x="750" y="100" width="120" height="60" class="manager" rx="5"/>
  <text x="810" y="125" class="label">UserManager</text>
  <text x="810" y="140" class="small-text">用户状态管理</text>
  
  <rect x="900" y="100" width="120" height="60" class="manager" rx="5"/>
  <text x="960" y="125" class="label">DeviceManager</text>
  <text x="960" y="140" class="small-text">设备状态管理</text>
  
  <rect x="1050" y="100" width="120" height="60" class="manager" rx="5"/>
  <text x="1110" y="125" class="label">MaskManager</text>
  <text x="1110" y="140" class="small-text">遮盖疗法管理</text>
  
  <rect x="1200" y="100" width="140" height="60" class="manager" rx="5"/>
  <text x="1270" y="125" class="label">GazeTrackingManager</text>
  <text x="1270" y="140" class="small-text">眼动追踪管理</text>
  
  <!-- 存储层 -->
  <rect x="850" y="200" width="120" height="60" class="storage" rx="5"/>
  <text x="910" y="225" class="label">MMKVManager</text>
  <text x="910" y="240" class="small-text">本地存储</text>
  
  <rect x="1000" y="200" width="120" height="60" class="storage" rx="5"/>
  <text x="1060" y="225" class="label">AtomicReference</text>
  <text x="1060" y="240" class="small-text">内存状态</text>
  
  <!-- 事件总线 -->
  <circle cx="1050" cy="330" r="60" class="eventbus"/>
  <text x="1050" y="325" class="label">LiveEventBus</text>
  <text x="1050" y="340" class="small-text">全局事件总线</text>
  
  <!-- Service层 -->
  <rect x="750" y="450" width="120" height="60" class="service" rx="5"/>
  <text x="810" y="475" class="label">GazeTrackService</text>
  <text x="810" y="490" class="small-text">眼动追踪服务</text>
  
  <rect x="900" y="450" width="120" height="60" class="service" rx="5"/>
  <text x="960" y="475" class="label">TrackingManager</text>
  <text x="960" y="490" class="small-text">追踪管理</text>
  
  <!-- 连接线 - 左侧MVVM -->
  <line x1="110" y1="160" x2="110" y2="200" class="arrow"/>
  <line x1="260" y1="160" x2="260" y2="200" class="arrow"/>
  <line x1="410" y1="160" x2="410" y2="200" class="arrow"/>
  
  <line x1="110" y1="260" x2="110" y2="300" class="arrow"/>
  <line x1="260" y1="260" x2="260" y2="300" class="arrow"/>
  <line x1="410" y1="260" x2="410" y2="300" class="arrow"/>
  
  <line x1="110" y1="360" x2="185" y2="400" class="arrow"/>
  <line x1="260" y1="360" x2="185" y2="400" class="arrow"/>
  <line x1="410" y1="360" x2="335" y2="400" class="arrow"/>
  
  <!-- 连接线 - 右侧Manager -->
  <line x1="810" y1="160" x2="910" y2="200" class="arrow"/>
  <line x1="960" y1="160" x2="910" y2="200" class="arrow"/>
  <line x1="1110" y1="160" x2="1060" y2="200" class="arrow"/>
  <line x1="1270" y1="160" x2="1060" y2="200" class="arrow"/>
  
  <!-- EventBus连接 -->
  <line x1="810" y1="160" x2="1000" y2="300" class="event-arrow"/>
  <line x1="960" y1="160" x2="1020" y2="300" class="event-arrow"/>
  <line x1="1110" y1="160" x2="1080" y2="300" class="event-arrow"/>
  <line x1="1270" y1="160" x2="1100" y2="300" class="event-arrow"/>
  
  <line x1="810" y1="450" x2="1000" y2="360" class="event-arrow"/>
  <line x1="960" y1="450" x2="1020" y2="360" class="event-arrow"/>
  
  <!-- 中间对比箭头 -->
  <line x1="500" y1="250" x2="720" y2="250" class="bidirectional"/>
  <text x="610" y="240" class="label">vs</text>
  
  <!-- 说明文字 -->
  <text x="250" y="550" class="subtitle">传统方式特点：</text>
  <text x="50" y="580" class="small-text">• ViewModel通过lazy创建Repository实例</text>
  <text x="50" y="600" class="small-text">• Repository直接调用MpdRetrofitClient.createService()</text>
  <text x="50" y="620" class="small-text">• 通过LiveData在Activity和ViewModel间通信</text>
  <text x="50" y="640" class="small-text">• 每个组件自己管理依赖关系</text>
  
  <text x="950" y="550" class="subtitle">Manager + EventBus方式特点：</text>
  <text x="750" y="580" class="small-text">• 使用object单例Manager管理全局状态</text>
  <text x="750" y="600" class="small-text">• MMKVManager统一管理本地存储</text>
  <text x="750" y="620" class="small-text">• LiveEventBus实现跨组件事件通信</text>
  <text x="750" y="640" class="small-text">• AtomicReference保证线程安全</text>
  <text x="750" y="660" class="small-text">• Service独立进程处理复杂业务逻辑</text>
  
  <!-- 核心优势 -->
  <rect x="500" y="700" width="400" height="150" fill="#2c3e50" stroke="#34495e" stroke-width="2" rx="10"/>
  <text x="700" y="730" class="title" fill="white">无依赖注入的核心优势</text>
  <text x="520" y="760" class="small-text" fill="white">1. 简单直接：无需学习复杂的DI框架</text>
  <text x="520" y="780" class="small-text" fill="white">2. 性能优异：避免反射和运行时解析开销</text>
  <text x="520" y="800" class="small-text" fill="white">3. 调试友好：依赖关系清晰可见</text>
  <text x="520" y="820" class="small-text" fill="white">4. 灵活控制：可精确控制对象生命周期</text>
  <text x="520" y="840" class="small-text" fill="white">5. 减少复杂性：降低项目整体复杂度</text>
</svg>
