# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Android medical application (MPD - Medical Precision Detection) developed for AirDoc that performs gaze tracking and eye-related medical diagnostics. The app combines Java/Kotlin Android development with native C++ code for computer vision processing using OpenCV and machine learning models.

## Key Architecture Components

### Core Application Structure
- **Package**: `com.airdoc.mpd`
- **Main Activities**: DetectionActivity1 (primary detection UI), MainActivity (entry point), HrvActivity (heart rate variability)
- **Services**: GazeTrackService (foreground service for eye tracking in separate process)

### Gaze Tracking System
The application's core functionality revolves around real-time gaze tracking:

- **GazeTrackingManager**: Central singleton managing the gaze tracking lifecycle
- **GazeTrackService**: Foreground service running in `:gaze` process for camera operations
- **GTCameraManager**: Handles CameraX integration and image capture
- **TrackingManager**: Coordinates between native C++ tracking and Android UI
- **GazeWebSocketService**: WebSocket server for real-time gaze data streaming

### Native C++ Components
Located in `app/src/main/cpp/`:
- **GazeTracker.cpp**: Core gaze tracking algorithms
- **detection.cpp**: Face and eye detection using OpenCV
- **eye_track.cpp**: Eye tracking implementation
- **calibration.cpp**: Gaze calibration routines
- **GazeService.cpp**: JNI bridge between Java and C++

### Key Dependencies
- **CameraX**: Modern camera API for image capture
- **OpenCV**: Computer vision processing (included as native library)
- **RKNN**: Rockchip neural network runtime for ML models
- **WebSocket**: Real-time data communication
- **LiveEventBus**: Event-driven communication between components

## Build Commands

### Standard Android Build
```bash
# Build debug APK
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease

# Install debug build to connected device
./gradlew installDebug

# Clean build
./gradlew clean
```

### Native Development
```bash
# Build only native libraries
./gradlew :app:buildCMakeDebug[arm64-v8a]
./gradlew :app:buildCMakeRelease[arm64-v8a]
```

### Testing
```bash
# Run unit tests
./gradlew test

# Run instrumentation tests
./gradlew connectedAndroidTest
```

## Development Setup

### Prerequisites
- Android Studio Arctic Fox or later
- NDK version 25.1.8937393 (specified in build.gradle.kts)
- CMake 3.22.1 or later
- Target SDK: 34, Min SDK: 29
- Supports only arm64-v8a architecture

### Configuration Files
- **libs.versions.toml**: Centralized dependency version management
- **CMakeLists.txt**: Native C++ build configuration
- **proguard-rules.pro**: Code obfuscation rules for release builds

## Architecture Patterns

### Service Communication
- Uses Android Messenger pattern for IPC between main app and GazeTrackService
- LiveEventBus for loose coupling between Android components
- WebSocket for external communication of gaze data

### Lifecycle Management
- GazeTrackService implements LifecycleOwner for proper resource management
- Coroutines with lifecycle-aware scopes for async operations
- Proper camera resource cleanup on lifecycle events

### Data Flow
1. Camera captures frames → GTCameraManager
2. Frames processed by native C++ → GazeTracker
3. Results passed to TrackingManager → GazeTrackingManager
4. Data streamed via WebSocket → External consumers
5. UI updates via LiveEventBus events

## Critical Implementation Details

### Process Separation
The GazeTrackService runs in a separate `:gaze` process to isolate camera operations and prevent ANRs in the main UI thread.

### Permission Requirements
- CAMERA, SYSTEM_CAMERA (system-level camera access)
- FOREGROUND_SERVICE_CAMERA
- SYSTEM_ALERT_WINDOW (overlay permissions)
- Various storage and network permissions

### Asset Management
- ML models stored in `assets/configs/` (*.rknn files)
- Calibration parameters and OpenCV.js included
- Multi-language audio assets in `raw/` and `raw-en/`

## Common Development Tasks

When modifying gaze tracking functionality, always consider:
1. Synchronization between Java/Kotlin and C++ layers
2. Proper camera resource lifecycle management
3. WebSocket connection state management
4. Process communication reliability
5. Performance impact on real-time processing

The codebase follows a hybrid architecture combining Android Architecture Components (ViewModels, LiveData) with custom managers for specialized hardware integration.