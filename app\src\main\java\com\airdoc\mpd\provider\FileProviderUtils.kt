package com.airdoc.mpd.provider

import android.content.Context
import android.net.Uri
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream

/**
 * FileProvider工具类
 * 提供便捷的文件操作方法
 */
object FileProviderUtils {
    
    private const val TAG = "FileProviderUtils"
    
    /**
     * 确保配置目录存在
     * @param context 上下文
     * @return 配置目录File对象
     */
    fun ensureConfigDirectory(context: Context): File {
        val configDir = File(context.filesDir, "app_configs")
        if (!configDir.exists()) {
            configDir.mkdirs()
            Log.d(TAG, "Created config directory: ${configDir.absolutePath}")
        }
        return configDir
    }
    
    /**
     * 获取配置文件的Content URI
     * @param fileName 文件名
     * @return Content URI字符串
     */
    fun getConfigFileContentUri(fileName: String): String {
        return MpdFileProvider.getConfigFileUri(fileName).toString()
    }
    
    /**
     * 获取普通文件的Content URI
     * @param fileName 文件名
     * @return Content URI字符串
     */
    fun getFileContentUri(fileName: String): String {
        return MpdFileProvider.getFileUri(fileName).toString()
    }
    
    /**
     * 检查配置文件是否存在
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件是否存在
     */
    fun isConfigFileExists(context: Context, fileName: String): Boolean {
        val configDir = File(context.filesDir, "app_configs")
        val file = File(configDir, fileName)
        return file.exists()
    }
    
    /**
     * 创建配置文件
     * @param context 上下文
     * @param fileName 文件名
     * @param content 文件内容
     * @return 是否创建成功
     */
    fun createConfigFile(context: Context, fileName: String, content: String): Boolean {
        return try {
            val configDir = ensureConfigDirectory(context)
            val file = File(configDir, fileName)
            
            FileOutputStream(file).use { fos ->
                fos.write(content.toByteArray())
            }
            
            Log.d(TAG, "Created config file: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create config file: $fileName", e)
            false
        }
    }
    
    /**
     * 读取配置文件内容
     * @param context 上下文
     * @param fileName 文件名
     * @return 文件内容，失败返回null
     */
    fun readConfigFile(context: Context, fileName: String): String? {
        return try {
            val configDir = File(context.filesDir, "app_configs")
            val file = File(configDir, fileName)
            
            if (!file.exists()) {
                Log.w(TAG, "Config file not found: $fileName")
                return null
            }
            
            FileInputStream(file).use { fis ->
                fis.readBytes().toString(Charsets.UTF_8)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to read config file: $fileName", e)
            null
        }
    }
    
    /**
     * 复制文件到配置目录
     * @param context 上下文
     * @param sourceFile 源文件
     * @param targetFileName 目标文件名
     * @return 是否复制成功
     */
    fun copyToConfigDirectory(context: Context, sourceFile: File, targetFileName: String): Boolean {
        return try {
            val configDir = ensureConfigDirectory(context)
            val targetFile = File(configDir, targetFileName)
            
            FileInputStream(sourceFile).use { input ->
                FileOutputStream(targetFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            Log.d(TAG, "Copied file to config directory: $targetFileName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy file to config directory: $targetFileName", e)
            false
        }
    }
    
    /**
     * 从InputStream复制到配置目录
     * @param context 上下文
     * @param inputStream 输入流
     * @param targetFileName 目标文件名
     * @return 是否复制成功
     */
    fun copyToConfigDirectory(context: Context, inputStream: InputStream, targetFileName: String): Boolean {
        return try {
            val configDir = ensureConfigDirectory(context)
            val targetFile = File(configDir, targetFileName)
            
            inputStream.use { input ->
                FileOutputStream(targetFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            Log.d(TAG, "Copied stream to config directory: $targetFileName")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to copy stream to config directory: $targetFileName", e)
            false
        }
    }
    
    /**
     * 删除配置文件
     * @param context 上下文
     * @param fileName 文件名
     * @return 是否删除成功
     */
    fun deleteConfigFile(context: Context, fileName: String): Boolean {
        return try {
            val configDir = File(context.filesDir, "app_configs")
            val file = File(configDir, fileName)
            
            val result = file.delete()
            if (result) {
                Log.d(TAG, "Deleted config file: $fileName")
            } else {
                Log.w(TAG, "Failed to delete config file (may not exist): $fileName")
            }
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting config file: $fileName", e)
            false
        }
    }
    
    /**
     * 列出配置目录中的所有文件
     * @param context 上下文
     * @return 文件名列表
     */
    fun listConfigFiles(context: Context): List<String> {
        return try {
            val configDir = File(context.filesDir, "app_configs")
            if (!configDir.exists()) {
                emptyList()
            } else {
                configDir.listFiles()?.map { it.name } ?: emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error listing config files", e)
            emptyList()
        }
    }
}
