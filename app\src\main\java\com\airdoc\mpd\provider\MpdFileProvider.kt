package com.airdoc.mpd.provider

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.util.Log
import java.io.File
import java.io.FileNotFoundException

/**
 * MPD应用的文件提供者
 * 用于安全地向其他组件（如WebView）提供文件访问
 * 
 * 支持的URI格式：
 * - content://com.airdoc.mpd.provider/configs/filename - 访问app_configs目录下的文件
 * - content://com.airdoc.mpd.provider/files/filename - 访问应用内部存储文件
 */
class MpdFileProvider : ContentProvider() {
    
    companion object {
        private const val TAG = "MpdFileProvider"
        private const val AUTHORITY = "com.airdoc.mpd.provider"
        
        /**
         * 获取配置文件的Content URI
         * @param fileName 文件名
         * @return Content URI
         */
        fun getConfigFileUri(fileName: String): Uri {
            return Uri.parse("content://$AUTHORITY/configs/$fileName")
        }
        
        /**
         * 获取普通文件的Content URI
         * @param fileName 文件名
         * @return Content URI
         */
        fun getFileUri(fileName: String): Uri {
            return Uri.parse("content://$AUTHORITY/files/$fileName")
        }
    }
    
    override fun onCreate(): Boolean {
        Log.d(TAG, "MpdFileProvider onCreate")
        return true
    }
    
    override fun openFile(uri: Uri, mode: String): ParcelFileDescriptor? {
        Log.d(TAG, "openFile: uri=$uri, mode=$mode")
        
        val path = uri.path ?: throw FileNotFoundException("Invalid URI path: $uri")
        
        val file = when {
            path.startsWith("/configs/") -> {
                // 访问配置文件目录
                val fileName = path.substring("/configs/".length)
                val configDir = File(context?.filesDir, "app_configs")
                File(configDir, fileName)
            }
            path.startsWith("/files/") -> {
                // 访问应用内部文件
                val fileName = path.substring("/files/".length)
                File(context?.filesDir, fileName)
            }
            else -> {
                throw FileNotFoundException("Unsupported path: $path")
            }
        }
        
        Log.d(TAG, "Accessing file: ${file.absolutePath}")
        
        if (!file.exists()) {
            Log.e(TAG, "File not found: ${file.absolutePath}")
            throw FileNotFoundException("File not found: ${file.absolutePath}")
        }
        
        if (!file.canRead()) {
            Log.e(TAG, "File not readable: ${file.absolutePath}")
            throw FileNotFoundException("File not readable: ${file.absolutePath}")
        }
        
        // 根据mode参数确定打开模式
        val fileMode = when (mode) {
            "r" -> ParcelFileDescriptor.MODE_READ_ONLY
            "w" -> ParcelFileDescriptor.MODE_WRITE_ONLY
            "rw" -> ParcelFileDescriptor.MODE_READ_WRITE
            else -> ParcelFileDescriptor.MODE_READ_ONLY
        }
        
        return try {
            ParcelFileDescriptor.open(file, fileMode)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open file: ${file.absolutePath}", e)
            throw FileNotFoundException("Failed to open file: ${e.message}")
        }
    }
    
    override fun getType(uri: Uri): String? {
        Log.d(TAG, "getType: uri=$uri")
        
        val path = uri.path ?: return null
        
        return when {
            path.endsWith(".json") -> "application/json"
            path.endsWith(".txt") -> "text/plain"
            path.endsWith(".html") -> "text/html"
            path.endsWith(".css") -> "text/css"
            path.endsWith(".js") -> "application/javascript"
            path.endsWith(".png") -> "image/png"
            path.endsWith(".jpg") || path.endsWith(".jpeg") -> "image/jpeg"
            path.endsWith(".gif") -> "image/gif"
            path.endsWith(".pdf") -> "application/pdf"
            else -> "application/octet-stream"
        }
    }
    
    // 以下方法为ContentProvider必须实现的方法，但在文件提供者中通常返回null或0
    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        Log.d(TAG, "query: uri=$uri (not supported)")
        return null
    }
    
    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        Log.d(TAG, "insert: uri=$uri (not supported)")
        return null
    }
    
    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        Log.d(TAG, "delete: uri=$uri (not supported)")
        return 0
    }
    
    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        Log.d(TAG, "update: uri=$uri (not supported)")
        return 0
    }
}
