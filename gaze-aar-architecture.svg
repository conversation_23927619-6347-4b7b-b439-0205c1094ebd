<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #555; }
      .text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
      .method-text { font-family: Consolas, monospace; font-size: 8px; fill: #444; }
      .api-box { fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; }
      .service-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 2; }
      .jni-box { fill: #fff3e0; stroke: #ef6c00; stroke-width: 2; }
      .data-box { fill: #e8f5e8; stroke: #388e3c; stroke-width: 2; }
      .ext-box { fill: #fce4ec; stroke: #c2185b; stroke-width: 2; }
      .camera-box { fill: #f1f8e9; stroke: #689f38; stroke-width: 2; }
      .calib-box { fill: #fff8e1; stroke: #ffa000; stroke-width: 2; }
      .util-box { fill: #f3e5f5; stroke: #8e24aa; stroke-width: 2; }
      .widget-box { fill: #e8eaf6; stroke: #3f51b5; stroke-width: 2; }
      .manager-box { fill: #fafafa; stroke: #424242; stroke-width: 2; }
      .arrow { stroke: #666; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #4caf50; stroke-width: 2; fill: none; marker-end: url(#datahead); }
      .control-arrow { stroke: #f44336; stroke-width: 2; fill: none; marker-end: url(#controlhead); }
      .group-border { fill: none; stroke: #999; stroke-width: 2; stroke-dasharray: 5,5; }
      .inner-border { fill: none; stroke: #ccc; stroke-width: 1; stroke-dasharray: 3,3; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="datahead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50" />
    </marker>
    <marker id="controlhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f44336" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">眼动追踪AAR封装详细架构图</text>
  <text x="800" y="50" text-anchor="middle" class="text">Gaze Tracking SDK Architecture - Detailed View</text>

  <!-- AAR Library 主框架 -->
  <rect x="50" y="70" width="1500" height="1050" class="group-border" rx="15"/>
  <text x="70" y="95" class="subtitle">AAR Library - com.airdoc.gaze</text>
  
  <!-- Public API Layer -->
  <rect x="70" y="110" width="450" height="180" class="api-box" rx="8"/>
  <text x="80" y="130" class="subtitle">Public API Layer - 公开接口层</text>

  <!-- GazeTrackingSDK -->
  <rect x="80" y="140" width="200" height="120" fill="white" stroke="#0277bd" stroke-width="2" rx="5"/>
  <text x="90" y="155" class="text">GazeTrackingSDK (Singleton)</text>
  <text x="90" y="170" class="method-text">+ init(context, config): Boolean</text>
  <text x="90" y="180" class="method-text">+ startTracking(listener): Boolean</text>
  <text x="90" y="190" class="method-text">+ stopTracking(): Boolean</text>
  <text x="90" y="200" class="method-text">+ startCalibration(mode): Boolean</text>
  <text x="90" y="210" class="method-text">+ isTracking(): Boolean</text>
  <text x="90" y="220" class="method-text">+ release(): Void</text>
  <text x="90" y="230" class="method-text">+ getVersion(): String</text>
  <text x="90" y="240" class="method-text">+ checkPermissions(): Boolean</text>
  <text x="90" y="250" class="small-text">状态: INITIALIZED, TRACKING, IDLE</text>

  <!-- Listener Interfaces -->
  <rect x="300" y="140" width="200" height="120" fill="white" stroke="#0277bd" stroke-width="2" rx="5"/>
  <text x="310" y="155" class="text">Listener Interfaces</text>
  <text x="310" y="170" class="method-text">IGazeTrackListener:</text>
  <text x="315" y="180" class="method-text">- onGazeTracking(result)</text>
  <text x="315" y="190" class="method-text">- onServiceModeChange(mode)</text>
  <text x="315" y="200" class="method-text">- onError(error)</text>
  <text x="310" y="215" class="method-text">ICalibrati    onListener:</text>
  <text x="315" y="225" class="method-text">- onCalibrationComplete(result)</text>
  <text x="315" y="235" class="method-text">- onCalibrationProgress(progress)</text>
  <text x="315" y="245" class="method-text">- onCalibrationError(error)</text>
  
  <!-- Core Service Layer -->
  <rect x="550" y="110" width="650" height="180" class="service-box" rx="8"/>
  <text x="560" y="130" class="subtitle">Core Service Layer - 核心服务层</text>

  <!-- GazeTrackService -->
  <rect x="560" y="140" width="200" height="120" fill="white" stroke="#7b1fa2" stroke-width="2" rx="5"/>
  <text x="570" y="155" class="text">GazeTrackService (独立进程)</text>
  <text x="570" y="170" class="method-text">+ onCreate(): Void</text>
  <text x="570" y="180" class="method-text">+ onStartCommand(): Int</text>
  <text x="570" y="190" class="method-text">+ onBind(): IBinder</text>
  <text x="570" y="200" class="method-text">- startGazeTrack(): Void</text>
  <text x="570" y="210" class="method-text">- stopGazeTrack(): Void</text>
  <text x="570" y="220" class="method-text">- handleMessage(msg): Void</text>
  <text x="570" y="230" class="method-text">- initListener(): Void</text>
  <text x="570" y="240" class="method-text">- copyModel2Dir(): Void</text>
  <text x="570" y="250" class="small-text">进程: :gazetrack</text>

  <!-- GazeTrackingManager -->
  <rect x="780" y="140" width="200" height="120" fill="white" stroke="#7b1fa2" stroke-width="2" rx="5"/>
  <text x="790" y="155" class="text">GazeTrackingManager</text>
  <text x="790" y="170" class="method-text">+ getInstance(): Manager</text>
  <text x="790" y="180" class="method-text">+ init(context): Boolean</text>
  <text x="790" y="190" class="method-text">+ startTracking(): Boolean</text>
  <text x="790" y="200" class="method-text">+ stopTracking(): Boolean</text>
  <text x="790" y="210" class="method-text">+ startCalibration(): Boolean</text>
  <text x="790" y="220" class="method-text">+ setListener(listener): Void</text>
  <text x="790" y="230" class="method-text">+ getTrackingState(): State</text>
  <text x="790" y="240" class="method-text">+ release(): Void</text>
  <text x="790" y="250" class="small-text">单例模式</text>

  <!-- TrackingManager -->
  <rect x="1000" y="140" width="180" height="120" fill="white" stroke="#7b1fa2" stroke-width="2" rx="5"/>
  <text x="1010" y="155" class="text">TrackingManager</text>
  <text x="1010" y="170" class="method-text">+ init(context): Boolean</text>
  <text x="1010" y="180" class="method-text">+ startTracking(): Int</text>
  <text x="1010" y="190" class="method-text">+ stopTracking(): Int</text>
  <text x="1010" y="200" class="method-text">+ sendImageProxy(image): Void</text>
  <text x="1010" y="210" class="method-text">+ setGazeTrackListener(): Void</text>
  <text x="1010" y="220" class="method-text">+ destroy(): Void</text>
  <text x="1010" y="230" class="method-text">+ checkCalibrationParam(): Boolean</text>
  <text x="1010" y="240" class="small-text">状态管理: AtomicReference</text>
  <text x="1010" y="250" class="small-text">模式: TRACK/CALIBRATION</text>
  
  <!-- JNI Bridge Layer -->
  <rect x="1220" y="110" width="320" height="180" class="jni-box" rx="8"/>
  <text x="1230" y="130" class="subtitle">JNI Bridge Layer - JNI桥接层</text>

  <!-- GazeTrack JNI -->
  <rect x="1230" y="140" width="140" height="120" fill="white" stroke="#ef6c00" stroke-width="2" rx="5"/>
  <text x="1240" y="155" class="text">GazeTrack (JNI)</text>
  <text x="1240" y="170" class="method-text">+ nativeCreateObject(): Long</text>
  <text x="1240" y="180" class="method-text">+ nativeStartTracking(): Boolean</text>
  <text x="1240" y="190" class="method-text">+ nativeStopTracking(): Boolean</text>
  <text x="1240" y="200" class="method-text">+ nativeGazeTracking(): Void</text>
  <text x="1240" y="210" class="method-text">+ nativeCalibration(): Void</text>
  <text x="1240" y="220" class="method-text">+ nativeRelease(): Void</text>
  <text x="1240" y="230" class="method-text">+ setGazeTrackCallback(): Void</text>
  <text x="1240" y="240" class="small-text">回调: NativeGazeTrackCallback</text>
  <text x="1240" y="250" class="small-text">库: libmpd_app_dev.so</text>

  <!-- Native C++ Layer -->
  <rect x="1390" y="140" width="140" height="120" fill="white" stroke="#ef6c00" stroke-width="2" rx="5"/>
  <text x="1400" y="155" class="text">Native C++ Layer</text>
  <text x="1400" y="170" class="method-text">GazeService.cpp:</text>
  <text x="1405" y="180" class="method-text">- startTracking()</text>
  <text x="1405" y="190" class="method-text">- gazeTracking()</text>
  <text x="1405" y="200" class="method-text">- calibration()</text>
  <text x="1400" y="215" class="method-text">GazeApplication.cpp:</text>
  <text x="1405" y="225" class="method-text">- appliedModeChange()</text>
  <text x="1400" y="240" class="small-text">算法: RKNN推理引擎</text>
  <text x="1400" y="250" class="small-text">模型: .rknn格式</text>
  
  <!-- Camera & Processing -->
  <rect x="70" y="310" width="450" height="160" class="camera-box" rx="8"/>
  <text x="80" y="330" class="subtitle">Camera & Processing - 相机与图像处理层</text>

  <!-- GTCameraManager -->
  <rect x="80" y="340" width="200" height="100" fill="white" stroke="#689f38" stroke-width="2" rx="5"/>
  <text x="90" y="355" class="text">GTCameraManager</text>
  <text x="90" y="370" class="method-text">+ startCamera(context): Void</text>
  <text x="90" y="380" class="method-text">+ stopCamera(context): Void</text>
  <text x="90" y="390" class="method-text">+ setCameraListener(): Void</text>
  <text x="90" y="400" class="method-text">+ switchCamera(): Void</text>
  <text x="90" y="410" class="method-text">+ setResolution(size): Void</text>
  <text x="90" y="420" class="method-text">+ enableFillLight(): Void</text>
  <text x="90" y="430" class="small-text">相机ID: 0 (后置)</text>

  <!-- Image Processing -->
  <rect x="300" y="340" width="200" height="100" fill="white" stroke="#689f38" stroke-width="2" rx="5"/>
  <text x="310" y="355" class="text">Image Processing</text>
  <text x="310" y="370" class="method-text">ImageProxy处理:</text>
  <text x="315" y="380" class="method-text">- 分辨率: 4048x3040</text>
  <text x="315" y="390" class="method-text">- 格式: YUV_420_888</text>
  <text x="315" y="400" class="method-text">- 帧率: 30fps</text>
  <text x="310" y="415" class="method-text">ICameraListener:</text>
  <text x="315" y="425" class="method-text">- onAnalyze(image)</text>
  <text x="315" y="435" class="method-text">- onCameraStatusChange()</text>
  
  <!-- Calibration System -->
  <rect x="550" y="310" width="650" height="160" class="calib-box" rx="8"/>
  <text x="560" y="330" class="subtitle">Calibration System - 校准系统</text>

  <!-- CalibrationActivity -->
  <rect x="560" y="340" width="180" height="100" fill="white" stroke="#ffa000" stroke-width="2" rx="5"/>
  <text x="570" y="355" class="text">CalibrationActivity</text>
  <text x="570" y="370" class="method-text">+ onCreate(): Void</text>
  <text x="570" y="380" class="method-text">+ showPostureCalibration(): Void</text>
  <text x="570" y="390" class="method-text">+ showVisualCalibration(): Void</text>
  <text x="570" y="400" class="method-text">+ startTrack(): Void</text>
  <text x="570" y="410" class="method-text">+ calibrationComplete(): Void</text>
  <text x="570" y="420" class="method-text">+ turnOffCamera(): Void</text>
  <text x="570" y="430" class="small-text">模式: POSTURE/VISUAL</text>

  <!-- PostureCalibrationView -->
  <rect x="760" y="340" width="180" height="100" fill="white" stroke="#ffa000" stroke-width="2" rx="5"/>
  <text x="770" y="355" class="text">PostureCalibrationView</text>
  <text x="770" y="370" class="method-text">+ onPostureCalibration(): Void</text>
  <text x="770" y="380" class="method-text">+ showCalibrationTip(): Void</text>
  <text x="770" y="390" class="method-text">+ updateProgress(): Void</text>
  <text x="770" y="400" class="method-text">+ onCalibrationComplete(): Void</text>
  <text x="770" y="410" class="small-text">检测: 人脸位置和姿势</text>
  <text x="770" y="420" class="small-text">距离: 35-65cm</text>
  <text x="770" y="430" class="small-text">角度: ±15度范围</text>

  <!-- VisualCalibrationView -->
  <rect x="960" y="340" width="180" height="100" fill="white" stroke="#ffa000" stroke-width="2" rx="5"/>
  <text x="970" y="355" class="text">VisualCalibrationView</text>
  <text x="970" y="370" class="method-text">+ onCalibrating(): Void</text>
  <text x="970" y="380" class="method-text">+ showCalibrationPoint(): Void</text>
  <text x="970" y="390" class="method-text">+ updateCalibrationMatrix(): Void</text>
  <text x="970" y="400" class="method-text">+ saveCalibrationParam(): Void</text>
  <text x="970" y="410" class="small-text">校准点: 9点校准</text>
  <text x="970" y="420" class="small-text">矩阵: 左右眼映射</text>
  <text x="970" y="430" class="small-text">精度: 亚像素级别</text>
  
  <!-- Data Models -->
  <rect x="1220" y="310" width="320" height="160" class="data-box" rx="8"/>
  <text x="1230" y="330" class="subtitle">Data Models - 数据模型层</text>

  <!-- GazeTrackResult -->
  <rect x="1230" y="340" width="140" height="100" fill="white" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="1240" y="355" class="text">GazeTrackResult</text>
  <text x="1240" y="370" class="method-text">+ valid: Boolean</text>
  <text x="1240" y="380" class="method-text">+ skew: Boolean</text>
  <text x="1240" y="390" class="method-text">+ x: Float [0-1]</text>
  <text x="1240" y="400" class="method-text">+ y: Float [0-1]</text>
  <text x="1240" y="410" class="method-text">+ dist: Float (cm)</text>
  <text x="1240" y="420" class="method-text">+ duration: Int (ms)</text>
  <text x="1240" y="430" class="method-text">+ timestamp: Long</text>

  <!-- Other Data Models -->
  <rect x="1390" y="340" width="140" height="100" fill="white" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="1400" y="355" class="text">Other Models</text>
  <text x="1400" y="370" class="method-text">CalibrationResult:</text>
  <text x="1405" y="380" class="method-text">- isSuccess: Boolean</text>
  <text x="1405" y="390" class="method-text">- calibMatrix: Array</text>
  <text x="1400" y="405" class="method-text">Enums:</text>
  <text x="1405" y="415" class="method-text">- ServiceMode</text>
  <text x="1405" y="425" class="method-text">- CalibrationMode</text>
  <text x="1405" y="435" class="method-text">- AppliedMode</text>
  
  <!-- Utilities & Widgets -->
  <rect x="70" y="490" width="650" height="160" class="util-box" rx="8"/>
  <text x="80" y="510" class="subtitle">Utilities & Widgets - 工具与组件层</text>

  <!-- GTUtils -->
  <rect x="80" y="520" width="140" height="100" fill="white" stroke="#8e24aa" stroke-width="2" rx="5"/>
  <text x="90" y="535" class="text">GTUtils</text>
  <text x="90" y="550" class="method-text">+ copyAssets(): Boolean</text>
  <text x="90" y="560" class="method-text">+ checkPermissions(): Boolean</text>
  <text x="90" y="570" class="method-text">+ getDeviceInfo(): String</text>
  <text x="90" y="580" class="method-text">+ formatTimestamp(): String</text>
  <text x="90" y="590" class="method-text">+ calculateDistance(): Float</text>
  <text x="90" y="600" class="method-text">+ validateGazePoint(): Boolean</text>
  <text x="90" y="610" class="small-text">工具: 文件、权限、计算</text>

  <!-- WebSocket Service -->
  <rect x="240" y="520" width="140" height="100" fill="white" stroke="#8e24aa" stroke-width="2" rx="5"/>
  <text x="250" y="535" class="text">WebSocket Service</text>
  <text x="250" y="550" class="method-text">+ startServer(): Boolean</text>
  <text x="250" y="560" class="method-text">+ stopServer(): Void</text>
  <text x="250" y="570" class="method-text">+ sendGazeData(): Void</text>
  <text x="250" y="580" class="method-text">+ onMessage(): Void</text>
  <text x="250" y="590" class="method-text">+ onConnection(): Void</text>
  <text x="250" y="600" class="small-text">端口: 8080</text>
  <text x="250" y="610" class="small-text">协议: WebSocket</text>

  <!-- Widget Manager -->
  <rect x="400" y="520" width="140" height="100" fill="white" stroke="#8e24aa" stroke-width="2" rx="5"/>
  <text x="410" y="535" class="text">WidgetManager</text>
  <text x="410" y="550" class="method-text">+ showDotView(): Void</text>
  <text x="410" y="560" class="method-text">+ removeDotView(): Void</text>
  <text x="410" y="570" class="method-text">+ updateDotPosition(): Void</text>
  <text x="410" y="580" class="method-text">+ setDotColor(): Void</text>
  <text x="410" y="590" class="method-text">+ setDotSize(): Void</text>
  <text x="410" y="600" class="small-text">视点显示管理</text>
  <text x="410" y="610" class="small-text">悬浮窗口</text>

  <!-- Process Utils -->
  <rect x="560" y="520" width="140" height="100" fill="white" stroke="#8e24aa" stroke-width="2" rx="5"/>
  <text x="570" y="535" class="text">ProcessUtils</text>
  <text x="570" y="550" class="method-text">+ isServiceRunning(): Boolean</text>
  <text x="570" y="560" class="method-text">+ killProcess(): Void</text>
  <text x="570" y="570" class="method-text">+ getProcessInfo(): Info</text>
  <text x="570" y="580" class="method-text">+ checkMemoryUsage(): Long</text>
  <text x="570" y="590" class="method-text">+ logProcessState(): Void</text>
  <text x="570" y="600" class="small-text">进程管理</text>
  <text x="570" y="610" class="small-text">内存监控</text>
  
  <!-- Host Application -->
  <rect x="750" y="490" width="450" height="160" class="api-box" rx="8"/>
  <text x="760" y="510" class="subtitle">Host Application - 宿主应用</text>

  <!-- Main Application -->
  <rect x="760" y="520" width="200" height="100" fill="white" stroke="#0277bd" stroke-width="2" rx="5"/>
  <text x="770" y="535" class="text">Main Application</text>
  <text x="770" y="550" class="method-text">+ onCreate(): Void</text>
  <text x="770" y="560" class="method-text">+ initGazeSDK(): Boolean</text>
  <text x="770" y="570" class="method-text">+ requestPermissions(): Void</text>
  <text x="770" y="580" class="method-text">+ onDestroy(): Void</text>
  <text x="770" y="590" class="method-text">+ handleGazeResult(): Void</text>
  <text x="770" y="600" class="small-text">依赖: gaze-tracking-sdk.aar</text>
  <text x="770" y="610" class="small-text">权限: CAMERA, WRITE_STORAGE</text>

  <!-- Activity/Fragment -->
  <rect x="980" y="520" width="200" height="100" fill="white" stroke="#0277bd" stroke-width="2" rx="5"/>
  <text x="990" y="535" class="text">Activity/Fragment</text>
  <text x="990" y="550" class="method-text">+ startGazeTracking(): Void</text>
  <text x="990" y="560" class="method-text">+ stopGazeTracking(): Void</text>
  <text x="990" y="570" class="method-text">+ onGazeResult(result): Void</text>
  <text x="990" y="580" class="method-text">+ showCalibration(): Void</text>
  <text x="990" y="590" class="method-text">+ updateUI(gazePoint): Void</text>
  <text x="990" y="600" class="small-text">UI更新: 实时眼动点显示</text>
  <text x="990" y="610" class="small-text">生命周期: 绑定SDK状态</text>
  
  <!-- External Dependencies -->
  <rect x="1220" y="490" width="320" height="160" class="ext-box" rx="8"/>
  <text x="1230" y="510" class="subtitle">External Dependencies - 外部依赖</text>

  <!-- CameraX -->
  <rect x="1230" y="520" width="140" height="100" fill="white" stroke="#c2185b" stroke-width="2" rx="5"/>
  <text x="1240" y="535" class="text">CameraX Framework</text>
  <text x="1240" y="550" class="method-text">+ ProcessCameraProvider</text>
  <text x="1240" y="560" class="method-text">+ ImageAnalysis</text>
  <text x="1240" y="570" class="method-text">+ CameraSelector</text>
  <text x="1240" y="580" class="method-text">+ Preview</text>
  <text x="1240" y="590" class="method-text">+ ImageCapture</text>
  <text x="1240" y="600" class="small-text">版本: 1.3.1</text>
  <text x="1240" y="610" class="small-text">相机管理和图像分析</text>

  <!-- OpenCV & Models -->
  <rect x="1390" y="520" width="140" height="100" fill="white" stroke="#c2185b" stroke-width="2" rx="5"/>
  <text x="1400" y="535" class="text">OpenCV & Models</text>
  <text x="1400" y="550" class="method-text">OpenCV 4.x:</text>
  <text x="1405" y="560" class="method-text">- 图像预处理</text>
  <text x="1405" y="570" class="method-text">- 特征提取</text>
  <text x="1400" y="585" class="method-text">AI Models (.rknn):</text>
  <text x="1405" y="595" class="method-text">- 人脸检测模型</text>
  <text x="1405" y="605" class="method-text">- 眼动追踪模型</text>
  <text x="1400" y="615" class="small-text">推理引擎: RKNN</text>
  
  <!-- 连接线和数据流 -->
  <!-- API Layer Connections -->
  <line x1="520" y1="200" x2="550" y2="200" class="control-arrow"/>
  <text x="530" y="195" class="small-text">控制调用</text>

  <!-- Service Layer Connections -->
  <line x1="980" y1="200" x2="1220" y2="200" class="control-arrow"/>
  <text x="1090" y="195" class="small-text">JNI调用</text>

  <!-- Service to Camera -->
  <line x1="660" y1="290" x2="390" y2="340" class="control-arrow"/>
  <text x="500" y="320" class="small-text">相机控制</text>

  <!-- Service to Calibration -->
  <line x1="770" y1="290" x2="770" y2="310" class="control-arrow"/>
  <text x="775" y="305" class="small-text">校准控制</text>

  <!-- Camera to Service (Data Flow) -->
  <line x1="390" y1="340" x2="660" y2="290" class="data-arrow"/>
  <text x="500" y="310" class="small-text">图像数据</text>

  <!-- JNI to Service (Data Flow) -->
  <line x1="1220" y1="220" x2="980" y2="220" class="data-arrow"/>
  <text x="1090" y="235" class="small-text">追踪结果</text>

  <!-- Service to API (Data Flow) -->
  <line x1="550" y1="220" x2="520" y2="220" class="data-arrow"/>
  <text x="530" y="235" class="small-text">回调数据</text>

  <!-- Host App to API -->
  <line x1="750" y1="570" x2="520" y2="250" class="control-arrow"/>
  <text x="620" y="400" class="small-text">SDK调用</text>

  <!-- External Dependencies to Components -->
  <line x1="1220" y1="580" x2="390" y2="440" class="arrow"/>
  <text x="800" y="520" class="small-text">CameraX依赖</text>

  <line x1="1390" y1="520" x2="1390" y2="260" class="arrow"/>
  <text x="1395" y="390" class="small-text">OpenCV/模型</text>

  <!-- Utilities to Service -->
  <line x1="350" y1="520" x2="660" y2="290" class="arrow"/>
  <text x="480" y="400" class="small-text">工具支持</text>
  
  <!-- 数据流说明 -->
  <rect x="70" y="680" width="650" height="120" class="inner-border" rx="5"/>
  <text x="80" y="700" class="subtitle">数据流向说明 (Data Flow)</text>
  <text x="90" y="720" class="text">1. 宿主应用通过SDK API发起眼动追踪请求</text>
  <text x="90" y="735" class="text">2. GazeTrackingManager协调各组件，启动独立进程服务</text>
  <text x="90" y="750" class="text">3. GTCameraManager获取4048x3040分辨率图像数据</text>
  <text x="90" y="765" class="text">4. TrackingManager将图像通过JNI传递给Native算法层</text>
  <text x="90" y="780" class="text">5. Native层使用RKNN推理引擎处理图像，返回眼动坐标</text>
  <text x="90" y="795" class="text">6. 结果通过回调链返回到宿主应用，实现实时眼动追踪</text>

  <!-- 技术特性说明 -->
  <rect x="750" y="680" width="450" height="120" class="inner-border" rx="5"/>
  <text x="760" y="700" class="subtitle">技术特性 (Technical Features)</text>
  <text x="770" y="720" class="text">• 独立进程架构: 服务运行在:gazetrack进程，确保稳定性</text>
  <text x="770" y="735" class="text">• 高精度算法: 亚像素级眼动追踪，支持9点校准</text>
  <text x="770" y="750" class="text">• 实时性能: 30fps图像处理，低延迟响应</text>
  <text x="770" y="765" class="text">• 硬件适配: 专门优化4048x3040分辨率后置摄像头</text>
  <text x="770" y="780" class="text">• WebSocket支持: 实时数据传输和远程调试</text>
  <text x="770" y="795" class="text">• 完整校准: 姿势校准 + 视标校准双重保障</text>

  <!-- 图例 -->
  <rect x="1220" y="680" width="320" height="120" fill="white" stroke="#999" stroke-width="2" rx="8"/>
  <text x="1230" y="700" class="subtitle">图例 (Legend)</text>

  <rect x="1230" y="710" width="20" height="12" class="api-box"/>
  <text x="1260" y="720" class="small-text">API层 - 公开接口</text>

  <rect x="1230" y="725" width="20" height="12" class="service-box"/>
  <text x="1260" y="735" class="small-text">服务层 - 核心服务</text>

  <rect x="1230" y="740" width="20" height="12" class="jni-box"/>
  <text x="1260" y="750" class="small-text">JNI层 - 桥接层</text>

  <rect x="1230" y="755" width="20" height="12" class="data-box"/>
  <text x="1260" y="765" class="small-text">数据层 - 模型定义</text>

  <rect x="1230" y="770" width="20" height="12" class="ext-box"/>
  <text x="1260" y="780" class="small-text">外部依赖 - 第三方库</text>

  <rect x="1380" y="710" width="20" height="12" class="camera-box"/>
  <text x="1410" y="720" class="small-text">相机层 - 图像处理</text>

  <rect x="1380" y="725" width="20" height="12" class="calib-box"/>
  <text x="1410" y="735" class="small-text">校准层 - 校准系统</text>

  <rect x="1380" y="740" width="20" height="12" class="util-box"/>
  <text x="1410" y="750" class="small-text">工具层 - 辅助组件</text>

  <!-- 箭头图例 -->
  <line x1="1380" y1="765" x2="1420" y2="765" class="control-arrow"/>
  <text x="1430" y="770" class="small-text">控制流</text>

  <line x1="1380" y1="780" x2="1420" y2="780" class="data-arrow"/>
  <text x="1430" y="785" class="small-text">数据流</text>

  <line x1="1380" y1="795" x2="1420" y2="795" class="arrow"/>
  <text x="1430" y="800" class="small-text">依赖关系</text>

</svg>
