<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .actor-box { fill: #e6f3ff; stroke: #4682b4; stroke-width: 2; }
      .lifeline { stroke: #4682b4; stroke-width: 2; stroke-dasharray: 5,5; }
      .message { stroke: #2e4057; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-message { stroke: #666; stroke-width: 1; stroke-dasharray: 3,3; fill: none; marker-end: url(#arrowhead-return); }
      .activation { fill: #ffffcc; stroke: #4682b4; stroke-width: 1; }
      .text { fill: #2e4057; font-family: Arial, sans-serif; font-size: 11px; }
      .title { fill: #2e4057; font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }
      .alt-box { fill: none; stroke: #888; stroke-width: 1; }
      .alt-label { fill: #666; font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; }
      .loop-box { fill: none; stroke: #008000; stroke-width: 1; }
      .loop-label { fill: #008000; font-family: Arial, sans-serif; font-size: 10px; font-weight: bold; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2e4057" />
    </marker>
    <marker id="arrowhead-return" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">人脸识别自动打招呼 - 时序图</text>
  
  <!-- Actors -->
  <rect x="50" y="60" width="80" height="40" class="actor-box"/>
  <text x="90" y="85" text-anchor="middle" class="text" font-weight="bold">User</text>
  
  <rect x="180" y="60" width="100" height="40" class="actor-box"/>
  <text x="230" y="85" text-anchor="middle" class="text" font-weight="bold">MainActivity</text>
  
  <rect x="330" y="60" width="120" height="40" class="actor-box"/>
  <text x="390" y="85" text-anchor="middle" class="text" font-weight="bold">CameraXViewModel</text>
  
  <rect x="500" y="60" width="140" height="40" class="actor-box"/>
  <text x="570" y="85" text-anchor="middle" class="text" font-weight="bold">FaceDetectorProcessor</text>
  
  <rect x="690" y="60" width="100" height="40" class="actor-box"/>
  <text x="740" y="85" text-anchor="middle" class="text" font-weight="bold">Google ML Kit</text>
  
  <rect x="840" y="60" width="100" height="40" class="actor-box"/>
  <text x="890" y="85" text-anchor="middle" class="text" font-weight="bold">PlayManager</text>
  
  <rect x="990" y="60" width="120" height="40" class="actor-box"/>
  <text x="1050" y="85" text-anchor="middle" class="text" font-weight="bold">Audio Resources</text>
  
  <!-- Lifelines -->
  <line x1="90" y1="100" x2="90" y2="950" class="lifeline"/>
  <line x1="230" y1="100" x2="230" y2="950" class="lifeline"/>
  <line x1="390" y1="100" x2="390" y2="950" class="lifeline"/>
  <line x1="570" y1="100" x2="570" y2="950" class="lifeline"/>
  <line x1="740" y1="100" x2="740" y2="950" class="lifeline"/>
  <line x1="890" y1="100" x2="890" y2="950" class="lifeline"/>
  <line x1="1050" y1="100" x2="1050" y2="950" class="lifeline"/>
  
  <!-- Messages -->
  <!-- 1. User starts app -->
  <line x1="90" y1="130" x2="230" y2="130" class="message"/>
  <text x="140" y="125" class="text">启动应用</text>
  
  <!-- 2. onCreate -->
  <rect x="225" y="140" width="10" height="20" class="activation"/>
  <line x1="230" y1="150" x2="280" y2="150" class="message"/>
  <line x1="280" y1="150" x2="230" y2="150" class="return-message"/>
  <text x="240" y="145" class="text">onCreate()</text>
  
  <!-- 3. getProcessCameraProvider -->
  <line x1="230" y1="180" x2="390" y2="180" class="message"/>
  <text x="290" y="175" class="text">getProcessCameraProvider()</text>
  <rect x="385" y="185" width="10" height="15" class="activation"/>
  
  <!-- 4. cameraProviderLiveData update -->
  <line x1="390" y1="210" x2="230" y2="210" class="return-message"/>
  <text x="280" y="205" class="text">cameraProviderLiveData更新</text>
  
  <!-- Alt box for proactive greeting enabled -->
  <rect x="40" y="230" width="1100" height="650" class="alt-box"/>
  <text x="50" y="245" class="alt-label">alt [启用主动欢迎功能]</text>
  
  <!-- 5. bindAllCameraUseCases -->
  <rect x="225" y="260" width="10" height="20" class="activation"/>
  <line x1="230" y1="270" x2="280" y2="270" class="message"/>
  <line x1="280" y1="270" x2="230" y2="270" class="return-message"/>
  <text x="240" y="265" class="text">bindAllCameraUseCases()</text>
  
  <!-- 6. Setup ImageAnalysis -->
  <line x1="230" y1="300" x2="280" y2="300" class="message"/>
  <line x1="280" y1="300" x2="230" y2="300" class="return-message"/>
  <text x="240" y="295" class="text">设置ImageAnalysis分析器</text>
  
  <!-- Loop box for continuous image analysis -->
  <rect x="60" y="320" width="1000" height="480" class="loop-box"/>
  <text x="70" y="335" class="loop-label">loop [持续图像分析]</text>
  
  <!-- 7. detectInImage -->
  <line x1="230" y1="350" x2="570" y2="350" class="message"/>
  <text x="380" y="345" class="text">detectInImage()</text>
  <rect x="565" y="355" width="10" height="15" class="activation"/>
  
  <!-- 8. ML Kit process -->
  <line x1="570" y1="380" x2="740" y2="380" class="message"/>
  <text x="630" y="375" class="text">process(InputImage)</text>
  <rect x="735" y="385" width="10" height="15" class="activation"/>
  
  <!-- 9. Return face detection results -->
  <line x1="740" y1="410" x2="570" y2="410" class="return-message"/>
  <text x="630" y="405" class="text">返回人脸检测结果</text>
  
  <!-- Alt box for face detected -->
  <rect x="80" y="430" width="900" height="350" class="alt-box"/>
  <text x="90" y="445" class="alt-label">alt [检测到人脸]</text>
  
  <!-- 10. Face validation -->
  <line x1="570" y1="460" x2="620" y2="460" class="message"/>
  <line x1="620" y1="460" x2="570" y2="460" class="return-message"/>
  <text x="580" y="455" class="text">isFrontFace()</text>
  
  <line x1="570" y1="480" x2="620" y2="480" class="message"/>
  <line x1="620" y1="480" x2="570" y2="480" class="return-message"/>
  <text x="580" y="475" class="text">distanceCm()</text>
  
  <line x1="570" y1="500" x2="230" y2="500" class="return-message"/>
  <text x="380" y="495" class="text">人脸检测结果</text>
  
  <!-- Alt box for valid face -->
  <rect x="100" y="520" width="800" height="240" class="alt-box"/>
  <text x="110" y="535" class="alt-label">alt [正面人脸且距离&lt;80cm]</text>
  
  <!-- 11. detectedFace -->
  <line x1="230" y1="550" x2="280" y2="550" class="message"/>
  <line x1="280" y1="550" x2="230" y2="550" class="return-message"/>
  <text x="240" y="545" class="text">detectedFace()</text>
  
  <!-- Alt box for 2 seconds detection -->
  <rect x="120" y="570" width="700" height="170" class="alt-box"/>
  <text x="130" y="585" class="alt-label">alt [持续检测2秒且未播放过]</text>
  
  <!-- 12. playSpeechGuide -->
  <line x1="230" y1="600" x2="280" y2="600" class="message"/>
  <line x1="280" y1="600" x2="230" y2="600" class="return-message"/>
  <text x="240" y="595" class="text">playSpeechGuide()</text>
  
  <!-- 13. Load audio based on startup mode -->
  <line x1="230" y1="630" x2="1050" y2="630" class="message"/>
  <text x="620" y="625" class="text">加载相应音频文件</text>
  
  <!-- 14. Play audio -->
  <line x1="230" y1="660" x2="890" y2="660" class="message"/>
  <text x="540" y="655" class="text">playMediaSource()</text>
  
  <line x1="890" y1="690" x2="90" y2="690" class="message"/>
  <text x="470" y="685" class="text">播放语音引导</text>
  
  <line x1="230" y1="720" x2="280" y2="720" class="message"/>
  <line x1="280" y1="720" x2="230" y2="720" class="return-message"/>
  <text x="240" y="715" class="text">isPlayedSpeechGuide = true</text>
  
  <!-- Else branch for no face -->
  <line x1="80" y1="780" x2="980" y2="780" stroke="#888" stroke-width="1"/>
  <text x="90" y="795" class="alt-label">else [未检测到人脸]</text>
  
  <line x1="230" y1="810" x2="280" y2="810" class="message"/>
  <line x1="280" y1="810" x2="230" y2="810" class="return-message"/>
  <text x="240" y="805" class="text">noDetectedFace()</text>
  
  <!-- Alt for 10 seconds no face -->
  <rect x="120" y="830" width="400" height="40" class="alt-box"/>
  <text x="130" y="845" class="alt-label">alt [10秒无人脸检测]</text>
  <line x1="230" y1="860" x2="280" y2="860" class="message"/>
  <line x1="280" y1="860" x2="230" y2="860" class="return-message"/>
  <text x="240" y="855" class="text">isPlayedSpeechGuide = false</text>
  
  <!-- Else branch for disabled feature -->
  <line x1="40" y1="890" x2="1140" y2="890" stroke="#888" stroke-width="1"/>
  <text x="50" y="905" class="alt-label">else [未启用主动欢迎功能]</text>
  
  <line x1="230" y1="920" x2="280" y2="920" class="message"/>
  <line x1="280" y1="920" x2="230" y2="920" class="return-message"/>
  <text x="240" y="915" class="text">unBindAllCameraUseCases()</text>
</svg>